<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="cdb959e3-59c9-4d5c-9a75-bdae4d32d3db" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/SohuVod/app/src/main/java/com/sohuott/tv/vod/videodetail/activity/VideoStartTimeUtil.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/SohuVod/app/src/main/java/com/sohuott/tv/vod/app/App.kt" beforeDir="false" afterPath="$PROJECT_DIR$/SohuVod/app/src/main/java/com/sohuott/tv/vod/app/App.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/SohuVod/app/src/main/java/com/sohuott/tv/vod/videodetail/activity/VideoActivity.kt" beforeDir="false" afterPath="$PROJECT_DIR$/SohuVod/app/src/main/java/com/sohuott/tv/vod/videodetail/activity/VideoActivity.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/SohuVod/biz/biz-develop-helper/src/main/java/com/biz_device_info/BizLoggerConfigActivity.kt" beforeDir="false" afterPath="$PROJECT_DIR$/SohuVod/biz/biz-develop-helper/src/main/java/com/biz_device_info/BizLoggerConfigActivity.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/SohuVod/gradle/libs.versions.toml" beforeDir="false" afterPath="$PROJECT_DIR$/SohuVod/gradle/libs.versions.toml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitToolBoxStore">
    <option name="projectConfigVersion" value="5" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2Z3lA5dW6CGHnT21TKsSMEWK4hu" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "dart.analysis.tool.window.visible": "false",
    "git-widget-placeholder": "feature/drm__player__marlin",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "/Users/<USER>/Develop/Android/Sohu/ott-vod-app",
    "show.migrate.to.gradle.popup": "false"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="cdb959e3-59c9-4d5c-9a75-bdae4d32d3db" name="Changes" comment="" />
      <created>1701658496917</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1701658496917</updated>
    </task>
    <servers />
  </component>
</project>