<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/video_detail_decorView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_gradual_change_start_2b2b42_center_1b1a2b_end_080b17"
    android:clipChildren="false"
    android:clipToPadding="false"
    app:focusOutEnd="true"
    app:focusOutFront="true"
    tools:context=".videodetail.activity.VideoActivity">
    <com.sohuott.tv.vod.videodetail.activity.VideoDetailVListViewNew
        android:id="@+id/detail_recommend_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:focusOutEnd="true"
        app:focusOutFront="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/detail_sh_header_layout" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/detail_sh_header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:focusOutEnd="true"
        app:focusOutFront="true">

        <com.sohuott.tv.vod.widget.UserRelatedHeaderView
            android:id="@+id/header_view_video_detail_user"
            android:layout_width="match_parent"
            android:layout_height="@dimen/y132"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!--        video 占位使用-->
        <FrameLayout

            android:id="@+id/detail_sh_video_stub"
            android:layout_width="@dimen/x868"
            android:layout_height="@dimen/y488"
            android:layout_marginLeft="@dimen/x84"
            android:layout_marginBottom="@dimen/y10"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:visibility="invisible"
            app:focusOutEnd="true"
            app:focusOutFront="true"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/header_view_video_detail_user" />

        <!--    悬浮小窗简介-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/detail_video_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:paddingStart="@dimen/x48"
            android:focusable="false"
            app:layout_constraintBottom_toBottomOf="@+id/detail_sh_video_stub"
            app:layout_constraintStart_toEndOf="@+id/detail_sh_video_stub"
            app:layout_constraintTop_toTopOf="@+id/detail_sh_video_stub">

            <TextView
                android:id="@+id/tv_floating_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxWidth="@dimen/x836"
                android:textColor="#E8E8FF"
                android:textSize="@dimen/x48"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@id/video_detail_header_label"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="法医秦明第一季" />

            <LinearLayout
                android:id="@+id/video_detail_header_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/y24"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintBottom_toTopOf="@id/cl_scale_brief_introduction"
                app:layout_constraintStart_toStartOf="@id/tv_floating_name"
                app:layout_constraintTop_toBottomOf="@id/tv_floating_name">
                <!--        会员-->
                <TextView
                    android:id="@+id/tv_floating_members"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/y36"
                    android:background="@drawable/bg_video_detail_header_members"
                    android:text="会员"
                    android:textColor="#E7B99D"
                    android:textSize="@dimen/x26"
                    android:visibility="gone"
                    tools:visibility="visible" />
                <!--        评分Scoring-->
                <TextView
                    android:id="@+id/tv_floating_scoring"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/y36"
                    android:layout_marginStart="@dimen/x20"
                    android:background="@drawable/bg_video_detail_header_scoring"
                    android:text="8.9分"
                    android:textColor="#FF6247 "
                    android:textSize="@dimen/x26" />

                <!--        年份 类型-->
                <TextView
                    android:id="@+id/tv_floating_year"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/x20"
                    android:textColor="#CCE8E8FF"
                    android:textSize="@dimen/x26"
                    tools:text="2022 | 中国大陆｜悬疑" />
            </LinearLayout>

            <!--        简介Brief introduction-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:clickable="true"

                android:id="@+id/cl_scale_brief_introduction"
                android:layout_width="@dimen/x836"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/x20"
                android:background="@drawable/bg_gradual_change_start_2b2b42_end_4b4a5c_radius_10_angle_0"
                android:focusable="true"
                android:maxHeight="@dimen/y184"
                android:nextFocusRight="@id/cl_scale_brief_introduction"
                app:focusOutEnd="true"
                app:focusOutFront="true"
                app:layout_constraintBottom_toTopOf="@id/ll_video_detail_header_bottom_button"
                app:layout_constraintStart_toStartOf="@id/tv_floating_name"
                app:layout_constraintTop_toBottomOf="@id/video_detail_header_label">

                <TextView
                    android:id="@+id/tv_floating_brief_introduction"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:lineSpacingExtra="@dimen/x20"
                    android:maxLines="2"
                    android:textColor="#E6E8E8FF"
                    android:textSize="@dimen/x26"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_floating_brief_introduction_tip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/x20"
                    android:text="简介"
                    android:textColor="#E8E8FF"
                    android:textSize="@dimen/x26"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/iv_floating_brief_introduction_tip" />

                <ImageView
                    android:id="@+id/iv_floating_brief_introduction_tip"
                    android:layout_width="@dimen/x18"
                    android:layout_height="@dimen/y18"
                    android:layout_marginEnd="@dimen/x24"
                    android:src="@mipmap/ic_right"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_floating_brief_introduction_tip"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tv_floating_brief_introduction_tip" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <!--底部按钮-->
            <LinearLayout
                android:id="@+id/ll_video_detail_header_bottom_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:orientation="horizontal"
                android:paddingTop="@dimen/x20"
                app:focusOutEnd="true"
                app:focusOutFront="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="@id/tv_floating_name"
                app:layout_constraintTop_toBottomOf="@id/cl_scale_brief_introduction">
                <!--            全屏-->
                <LinearLayout
                    android:clickable="true"

                    android:id="@+id/btn_video_detail_header_full"
                    android:layout_width="@dimen/x155"
                    android:layout_height="@dimen/y130"
                    android:background="@drawable/bg_gradual_change_start_2b2b42_end_4b4a5c_radius_10_angle_0"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    android:focusable="true"
                    android:gravity="center"
                    android:nextFocusDown="@id/episode_tab_select"
                    android:orientation="vertical"
                    app:focusOutEnd="true"
                    app:focusOutFront="true">

                    <ImageView
                        android:id="@+id/iv_video_detail_header_full"
                        android:layout_width="@dimen/x56"
                        android:layout_height="@dimen/y56"
                        android:src="@mipmap/ic_full_not_selected" />

                    <TextView
                        android:id="@+id/tv_video_detail_header_full"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="全屏"
                        android:textColor="#B3E8E8FF"
                        android:textSize="@dimen/x32"
                        android:textStyle="bold" />
                </LinearLayout>

                <!--            购买-->
                <LinearLayout
                    android:clickable="true"

                    android:id="@+id/btn_video_detail_header_pay"

                    android:layout_width="@dimen/x155"
                    android:layout_height="@dimen/y130"
                    android:layout_marginStart="@dimen/x20"
                    android:background="@drawable/bg_gradual_change_start_2b2b42_end_4b4a5c_radius_10_angle_0"
                    android:focusable="true"
                    android:gravity="center"
                    android:nextFocusDown="@id/episode_tab_select"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/iv_video_detail_header_pay"
                        android:layout_width="@dimen/x56"
                        android:layout_height="@dimen/y56"
                        android:src="@mipmap/ic_pay_no_selected" />

                    <TextView
                        android:id="@+id/tv_video_detail_header_pay"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="购买"
                        android:textColor="#E7B99D"
                        android:textSize="@dimen/x32"
                        android:textStyle="bold" />
                </LinearLayout>

                <!--            活动-->
                <LinearLayout
                    android:clickable="true"

                    android:id="@+id/btn_video_detail_header_members"
                    android:layout_width="@dimen/x310"
                    android:layout_height="@dimen/y130"
                    android:layout_marginStart="@dimen/x20"
                    android:background="@drawable/bg_gradual_change_start_2b2b42_end_4b4a5c_radius_10_angle_0"
                    android:focusable="true"
                    android:gravity="center"
                    android:nextFocusDown="@id/episode_tab_select"
                    android:orientation="vertical">

                    <com.sohuott.tv.vod.customview.RoundedImageView
                        android:id="@+id/iv_video_detail_header_members"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="年卡限时4.9折"
                        android:textColor="#692910"
                        android:textSize="@dimen/x40" />
                </LinearLayout>

                <!--            收藏-->
                <LinearLayout
                    android:clickable="true"
                    android:id="@+id/btn_video_detail_header_collection"
                    android:layout_width="@dimen/x155"
                    android:layout_height="@dimen/y130"
                    android:layout_marginStart="@dimen/x20"
                    android:background="@drawable/bg_gradual_change_start_2b2b42_end_4b4a5c_radius_10_angle_0"
                    android:focusable="true"
                    android:gravity="center"
                    android:nextFocusRight="@id/btn_video_detail_header_collection"
                    android:nextFocusDown="@id/episode_tab_select"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/iv_video_detail_header_collection"
                        android:layout_width="@dimen/x56"
                        android:layout_height="@dimen/y56"
                        android:src="@mipmap/ic_collection_not_selected" />

                    <TextView
                        android:id="@+id/tv_video_detail_header_collection"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="收藏"
                        android:textColor="#B3E8E8FF"
                        android:textSize="@dimen/x32"
                        android:textStyle="bold" />
                </LinearLayout>

            </LinearLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>
        <!--        剩余时间-->
        <TextView
            android:id="@+id/tv_video_detail_header_remaining_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/x48"
            android:paddingTop="@dimen/x10"
            android:text="剩余观看时间"
            android:textColor="#80E8E8FF"
            android:textSize="@dimen/x26"
            android:visibility="invisible"
            app:layout_constraintStart_toStartOf="@id/detail_video_info"
            app:layout_constraintTop_toBottomOf="@id/detail_video_info" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.sh.ott.video.view.ShVideoView
        android:id="@+id/detail_sh_video"
        android:layout_width="@dimen/x868"
        android:layout_height="@dimen/y488"
        android:layout_marginLeft="@dimen/x84"
        android:layout_marginTop="@dimen/y132"
        android:layout_marginBottom="@dimen/y10"
        android:background="@color/black"
        android:focusable="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.sohuott.tv.vod.view.FocusBorderView
        android:id="@+id/fragment_item_focus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />
    <!--加载状态布局 start-->
    <com.sohuott.tv.vod.customview.LoadingView
        android:id="@+id/detail_loading_view"
        android:layout_width="@dimen/loading_size"
        android:layout_height="@dimen/loading_size"
        android:layout_gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/detail_error_layout"
        layout="@layout/layout_err"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" /><!--加载状态布局 end-->


</FrameLayout>