package com.sohuott.tv.vod.videodetail.activity

import android.content.Context
import android.graphics.Rect
import android.os.Handler
import android.os.Message
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.vlayout.DelegateAdapter
import com.alibaba.android.vlayout.VirtualLayoutManager
import com.alibaba.android.vlayout.layout.GridLayoutHelper
import com.alibaba.android.vlayout.layout.LinearLayoutHelper
import com.alibaba.android.vlayout.layout.SingleLayoutHelper
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.lib.log.LibDeprecatedLogger
import com.sohuott.tv.vod.lib.rvhelper.BaseViewHolder
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.lib.utils.StringUtils
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.videodetail.BaseDelegateAdapter
import com.sohuott.tv.vod.videodetail.data.model.VideoDetailRecommendModel
import com.sohuott.tv.vod.videodetail.horlist.DetailHorDelegateAdapter
import com.sohuott.tv.vod.videodetail.horlist.VideoDetailVListTitleAdapter
import com.sohuott.tv.vod.videodetail.vlist.VLayoutRecyclerView
import com.sohuott.tv.vod.widget.GlideImageView
import java.lang.ref.WeakReference
import java.util.LinkedList

class VideoDetailVListViewNew : VLayoutRecyclerView {
    private val MSG_LOAD_PIC = 1
    private var mHandler: Handler? = null
    private val adapters: LinkedList<DelegateAdapter.Adapter<BaseViewHolder>> = LinkedList()
    var recommendDataBean: VideoDetailRecommendModel.DataBean? = null

    private var loadAdapter: BaseDelegateAdapter? = null

    var mEpisodeGroupLayout: EpisodeGroupLayout? = null

    private var mEpisodeAdapter: BaseDelegateAdapter? = null

    private var mNeedFullscreenFocus = false

    var headerHasFocus = false

    private var mHeaderViewIsFloating = false

    //当前列表滑动的位置
    private var mScrollerPosition = NO_POSITION

    private var mOnListScrollChangeListener: OnListScrollChangeListener? = null


    constructor (context: Context) : this(context, null) {
    }

    constructor (context: Context, attrs: AttributeSet?) : this(context, attrs, 0) {
    }

    constructor (context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        setClipToPadding(false)
        setClipChildren(false)
        val viewPool = recycledViewPool
        viewPool.setMaxRecycledViews(Constant.DETAIL_VLIST_TYPE_ALBUM_4, 8)
        viewPool.setMaxRecycledViews(Constant.DETAIL_VLIST_TYPE_ALBUM_6, 12)
        mHandler = MyHandler(this)
        mEpisodeGroupLayout = EpisodeGroupLayout(context)
        addOnScrollListener(object : OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                //获取当前滑动的位置
                mScrollerPosition =
                    (layoutManager as LinearLayoutManager).findFirstVisibleItemPosition()
                //-1 没有找到 直接返回
                LibDeprecatedLogger.d("onScrollStateChanged mScrollerPosition=$mScrollerPosition, newState=$newState")
                if (mScrollerPosition == NO_POSITION) {
                    return
                }
                when (newState) {
                    SCROLL_STATE_SETTLING -> {
                        //暂停加载图片
//                        if (getContext().getActivityContext()?.isDestroyed == false) {
//                            GlideApp.with(context).pauseRequests()
//                        }
                        //选集和 header view 不获取焦点时 一定是 悬浮状态
                        if (mEpisodeGroupLayout?.hasFocus() == false && !headerHasFocus) {
                            mHeaderViewIsFloating = true
                            LibDeprecatedLogger.d("1 mHeaderViewIsFloating = $mHeaderViewIsFloating")
                        } else {
                            //todo 这个 判断滑动位置 是否大于0  是否可以去掉
                            mHeaderViewIsFloating = (mScrollerPosition >= 0)
                            LibDeprecatedLogger.d("2 mHeaderViewIsFloating = $mHeaderViewIsFloating")
                        }
                    }


                    SCROLL_STATE_IDLE -> {
                        val offset = 50
                        if (!windowFloating && recyclerView.computeVerticalScrollOffset() > offset) {
                            LibDeprecatedLogger.d("scrollBy : ${recyclerView.computeVerticalScrollOffset()}")
                            layoutManager?.scrollToPosition(0)
                        }
//                        if (getContext().getActivityContext()?.isDestroyed == false) {
//                            GlideApp.with(context).resumeRequests()
//                        }
                        mHandler?.removeMessages(MSG_LOAD_PIC)
                        mHandler?.sendEmptyMessageDelayed(MSG_LOAD_PIC, 200)
                        //当选集和 header view 获取焦点时一定是 不悬浮
                        if (mEpisodeGroupLayout?.hasFocus() == true || headerHasFocus) {
                            if (recyclerView.computeVerticalScrollOffset() < 150) {
                                mHeaderViewIsFloating = false
                            } else {
                                mHeaderViewIsFloating = true
                            }
                            LibDeprecatedLogger.d("3 mHeaderViewIsFloating = $mHeaderViewIsFloating $mScrollerPosition scrollY : ${recyclerView.computeVerticalScrollOffset()}")

                        } else {
                            mHeaderViewIsFloating = (mScrollerPosition >= 0 && recyclerView.computeVerticalScrollOffset() != 0)
                            LibDeprecatedLogger.d("4 mHeaderViewIsFloating = $mHeaderViewIsFloating $mScrollerPosition scrollY : ${recyclerView.computeVerticalScrollOffset()}")

                        }
                    }
                }
                LibDeprecatedLogger.d("windowFloating=$windowFloating, mHeaderViewIsFloating=$mHeaderViewIsFloating")
                if (windowFloating == mHeaderViewIsFloating) return
                mOnListScrollChangeListener?.onScrollWindowChange(mHeaderViewIsFloating)
                LibDeprecatedLogger.d("4 onScrollWindowChange(false)")
                if (newState == SCROLL_STATE_IDLE && Util.isSupportTouchVersion(getContext())) {
                    val firstView: View? = layoutManager?.findViewByPosition(
                        (layoutManager as LinearLayoutManager).findFirstVisibleItemPosition()
                    )
                    val rect = Rect()
                    firstView?.getLocalVisibleRect(rect)
                    val firstViewTop = resources.getDimension(R.dimen.y110)
                    if (rect.top <= firstViewTop) {
                        scrollBy(0, -rect.top)
                    }
                }
            }

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (dx == 0 && dy == 0) {
                    return
                }
            }
        })

    }


    fun getEpisodeGroupLayout(): EpisodeGroupLayout? {
        return mEpisodeGroupLayout
    }

    private fun getIsFloatingHeight(): Int {
        return if (windowFloating) {
            resources.getDimension(R.dimen.y372).toInt()
        } else {
            resources.getDimension(R.dimen.y664).toInt()
        }
    }

    /**
     * 更新选集
     */
    fun updateAfterPlayer(hasEpisode: Boolean, videoOrder: Int, isTrailerTab: Boolean, vid: Int) {
        mEpisodeGroupLayout?.updateAfterPlayer(hasEpisode, videoOrder, isTrailerTab, vid)
    }


    //初始化选集及默认加载数据
    fun onLoad(episodeGroupInfo: EpisodeGroupInfo) {
        mEpisodeGroupLayout?.setEpisodeGroupInfo(episodeGroupInfo)
        mNeedFullscreenFocus = true
        val loadingLayoutHelper = SingleLayoutHelper()
        loadingLayoutHelper.zIndex = Constant.DETAIL_VLIST_TYPE_LOADING
        loadingLayoutHelper.marginTop = resources.getDimensionPixelOffset(R.dimen.y34)
        removeList()
        updateScrollViewMarginTopParams()
        val singleLayoutHelper = GridLayoutHelper(1)
        singleLayoutHelper.zIndex = Constant.DETAIL_VLIST_TYPE_INTRO
        mEpisodeAdapter = object : BaseDelegateAdapter(
            context, singleLayoutHelper, 1, Constant.DETAIL_VLIST_TYPE_INTRO
        ) {
            override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
                val viewHolder = BaseViewHolder(mEpisodeGroupLayout)
                viewHolder.itemView.setTag(R.id.video_detail_recommend_adapter_index, 0)
                return viewHolder
            }

            override fun onBindViewHolder(holder: BaseViewHolder, position: Int) {
                super.onBindViewHolder(holder, position)
                if (mNeedFullscreenFocus) {
                    mEpisodeGroupLayout?.requestFocusDefault(true)
                    mNeedFullscreenFocus = false
                }
            }
        }
        mAdapter.addAdapter(mEpisodeAdapter)
        loadAdapter = object : BaseDelegateAdapter(
            getContext(),
            loadingLayoutHelper,
            1,
            Constant.DETAIL_VLIST_TYPE_LOADING
        ) {
            override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
                val loadView = TextView(getContext())
                loadView.setLayoutParams(
                    VirtualLayoutManager.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                )
                loadView.text = "正在全力加载数据"
                loadView.setGravity(Gravity.CENTER_HORIZONTAL)
                loadView.includeFontPadding = false
                loadView.setTextSize(
                    TypedValue.COMPLEX_UNIT_PX,
                    resources.getDimensionPixelOffset(R.dimen.y32).toFloat()
                )
                loadView.setTextColor(resources.getColor(R.color.video_detail_list_title))
                return BaseViewHolder(loadView)
            }
        }
        mAdapter.addAdapter(loadAdapter)

    }

    fun removeList() {
        adapters.clear()
        mAdapter.clear()
        mAdapter.notifyDataSetChanged()
    }

    fun stopInnerRequest(emptyPool: Boolean) {
        if (emptyPool) {
            recycledViewPool.clear()
        }
    }

    /**
     * 添加推荐数据
     */
    fun addRecommend(
        recommend: MutableList<VideoDetailRecommendModel.DataBean>?,
        hasTrailer: Boolean,
        videoType: Int
    ) {
        LibDeprecatedLogger.d("addRecommend mAdapter:${mAdapter.itemCount} adapters: ${adapters.size}")
        recommendDataBean = null
        val preIndex = if (hasTrailer) 2 else 1
        if (mAdapter.itemCount > 2) return
        if (recommend != null) {
            for (index in recommend.indices) {
                var name: String? = recommend.get(index).getName_new()
                if (StringUtils.isEmptyStr(name)) {
                    name = recommend.get(index).getName()
                }
                if (recommend.get(index).getContents().size > 5) {
                    adapters.add(createTitleAdapter(name ?: "", preIndex + 2 * index))
                    for (i in recommend.get(index).getContents().indices) {
                        recommend.get(index).getContents().get(i).setIndex(i)
                        recommend.get(index).getContents().get(i)
                            .setType(recommend.get(index).getType())
                        recommend.get(index).getContents().get(i)
                            .setTagName(recommend.get(index).getName())
                    }
                    val adapter = DetailHorDelegateAdapter(
                        this,
                        recommend.get(index),
                        videoType,
                        index == recommend.size - 1,
                        preIndex + 2 * index + 1
                    )
                    adapter.setFocusBorderView(mFocusBorderView)
                    adapters.add(adapter)
                    val dataBean = recommend.get(index)
                    if (dataBean.layoutType == Constant.DETAIL_VLIST_TYPE_ACTOR) {
                        recommendDataBean = dataBean
                    }
                }
            }
        }
        loadAdapter?.let {
            mAdapter.removeAdapter(it)
        }
        mHandler!!.removeMessages(MSG_LOAD_PIC)
        mHandler!!.sendEmptyMessageDelayed(MSG_LOAD_PIC, 100)
        mAdapter.addAdapters(adapters.toList())
        mAdapter.addAdapter(createFooterAdapter())
        adapters.clear()
    }

    private fun createTitleAdapter(title: String, index: Int): VideoDetailVListTitleAdapter {
        val layoutHelper = LinearLayoutHelper()
        layoutHelper.setMargin(
            resources.getDimensionPixelOffset(R.dimen.x92),
            0, 0, 0
        )
        layoutHelper.itemCount = 1
        return VideoDetailVListTitleAdapter(title, layoutHelper, index)
    }


    fun updateScrollViewMarginTopParams() {
        LibDeprecatedLogger.d("updateScrollViewMarginTopParams : windowFloating ： $windowFloating")
        val params: ViewGroup.LayoutParams = layoutParams
        var marginParams: ViewGroup.MarginLayoutParams? = null;
        if (params is ViewGroup.MarginLayoutParams) {
            marginParams = params as ViewGroup.MarginLayoutParams
        } else {
            marginParams = ViewGroup.MarginLayoutParams(params);
        }
        var marginTop = 0;
        if (windowFloating) {
            marginTop = resources.getDimension(R.dimen.y372).toInt()
        } else {
            marginTop = resources.getDimension(R.dimen.y664).toInt()
        }
        marginParams.setMargins(
            marginParams.leftMargin,
            marginTop,
            marginParams.rightMargin,
            marginParams.bottomMargin
        );
        layoutParams = marginParams
        if (!windowFloating) {
            scrollToPosition(0)
        }
    }

    private fun createFooterAdapter(): BaseDelegateAdapter {
        val footerLayoutHelper = SingleLayoutHelper()
        footerLayoutHelper.zIndex = Constant.DETAIL_VLIST_TYPE_FOOTER
        //        footerLayoutHelper.setMarginTop(getResources().getDimensionPixelOffset(R.dimen.y2));
        footerLayoutHelper.marginBottom = resources.getDimensionPixelOffset(R.dimen.y70)
        footerLayoutHelper.marginTop = resources.getDimensionPixelOffset(R.dimen.y90)
        return object :
            BaseDelegateAdapter(context, footerLayoutHelper, 1, Constant.DETAIL_VLIST_TYPE_FOOTER) {
            override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
                val footerView = TextView(context)
                footerView.setLayoutParams(
                    VirtualLayoutManager.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                )
                footerView.text = "请按【返回键】回到页面首页"
                footerView.setGravity(Gravity.CENTER)
                footerView.includeFontPadding = false
                footerView.setTextSize(
                    TypedValue.COMPLEX_UNIT_PX,
                    resources.getDimensionPixelOffset(R.dimen.y26).toFloat()
                )
                footerView.setTextColor(resources.getColor(R.color.tv_color_e8e8ff))
                return BaseViewHolder(footerView)
            }
        }
    }


//    override fun onScrollStateChanged(state: Int) {
//        super.onScrollStateChanged(state)
//        when (state) {
//            SCROLL_STATE_IDLE -> {
//                handleFocus()
//                mHandler!!.removeMessages(MSG_LOAD_PIC)
//                mHandler!!.sendEmptyMessageDelayed(MSG_LOAD_PIC, 200)
//                if (Util.isSupportTouchVersion(context)) {
//                    val firstView =
//                        mLayoutManager.findViewByPosition(mLayoutManager.findFirstVisibleItemPosition())
//                    val rect = Rect()
//                    val firstViewTop = resources.getDimension(R.dimen.y110)
//                    firstView!!.getLocalVisibleRect(rect)
//                    if (rect.top <= firstViewTop) {
//                        scrollBy(0, -rect.top)
//                    }
//                }
//            }
//
//            SCROLL_STATE_DRAGGING -> {}
//            else -> {}
//        }
//    }


    fun setImageResAfterScroll() {
        val firstPos = mLayoutManager.findFirstVisibleItemPosition()
        val lastPos = mLayoutManager.findLastVisibleItemPosition()
        for (pos in firstPos..lastPos) {
            val viewHolder = findViewHolderForAdapterPosition(pos) as BaseViewHolder? ?: continue
            val poster = viewHolder.getView<GlideImageView>(R.id.detail_recommend_poster)
            when (viewHolder.itemViewType) {
                Constant.DETAIL_VLIST_TYPE_INTRO, Constant.DETAIL_VLIST_TYPE_TRAILER, Constant.DETAIL_VLIST_TYPE_TITLE -> {}
                Constant.DETAIL_VLIST_TYPE_ACTOR -> if (viewHolder.itemView.getTag(R.id.video_detail_recommend_img_url) != null && poster != null) {
                    poster.setCircleImageRes(viewHolder.itemView.getTag(R.id.video_detail_recommend_img_url))
                }

                else -> if (viewHolder.itemView.getTag(R.id.video_detail_recommend_img_url) != null && poster != null) {
                    poster.setImageRes(viewHolder.itemView.getTag(R.id.video_detail_recommend_img_url))
                }
            }
        }
    }
    private var lastTimeMillis: Long = 0

    override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
        if (event?.action == KeyEvent.ACTION_DOWN) {
            lastTimeMillis = if (event.repeatCount == 0) {
                System.currentTimeMillis()
            } else {
                if (System.currentTimeMillis() - lastTimeMillis < 250) {
                    return true
                } else {
                    System.currentTimeMillis()
                }
            }
        }
        return super.dispatchKeyEvent(event)
    }

//    override fun handleFocus() {
//        val viewHolder = findViewHolderForAdapterPosition(mTargetPos)
//        if (viewHolder != null) {
//            if (viewHolder.itemView.findViewById<View?>(R.id.focus) != null) {
//                viewHolder.itemView.findViewById<View>(R.id.focus).requestFocus()
//            }
//        }
//        mSelectPos = mTargetPos
//    }

    //    private var lastTimeMillis: Long = 0
//    override fun dispatchKeyEvent(event: KeyEvent): Boolean {
//        if (event.action == KeyEvent.ACTION_DOWN) {
////            lastTimeMillis = if (event.repeatCount == 0) {
////                System.currentTimeMillis()
////            } else {
////                if (System.currentTimeMillis() - lastTimeMillis < 150) {
////                    return true
////                } else {
////                    System.currentTimeMillis()
////                }
////            }
//        } else {
//            when (event.keyCode) {
//                KeyEvent.KEYCODE_DPAD_UP -> {
//                    mScrollerPosition =
//                        (layoutManager as LinearLayoutManager).findFirstVisibleItemPosition()
//                    AppLogger.e(
//                        "VideoDetailVListViewNew KEYCODE_DPAD_UP  mScrollerPosition:${mScrollerPosition}"
//                    )
//                    val pos = mScrollerPosition - 2
//                    if (mScrollerPosition == 1 || pos == 0) {
//                        smoothScrollToPosition(1)
//                        return super.dispatchKeyEvent(event)
//                    }
//                    if (pos < 0) {
//                        smoothScrollToPosition(0)
//                        return super.dispatchKeyEvent(event)
//                    }
//                    smoothScrollToPosition(pos)
//                }
//            }
//        }
//        return super.dispatchKeyEvent(event)
//    }

    fun hasPendingAdapterItems(): Boolean {
        return adapters.size > 0
    }

    private var windowFloating: Boolean = false


    fun setVideoWindowFloating(model: Boolean) {
        windowFloating = model
    }


    inner class MyHandler(view: VideoDetailVListViewNew?) : Handler() {
        private val mView: WeakReference<VideoDetailVListViewNew>?

        init {
            mView = WeakReference(view)
        }

        override fun handleMessage(msg: Message) {
            if (mView == null) {
                return
            }
            val what = msg.what
            when (what) {
                MSG_LOAD_PIC -> if (mView.get() != null) {
                    mView.get()!!.setImageResAfterScroll()
                }

                else -> {}
            }
        }
    }


    interface OnListScrollChangeListener {

        fun onScrollWindowChange(headerViewIsFloating: Boolean)
    }

    fun setOnListScrollChangeListener(listener: OnListScrollChangeListener) {
        mOnListScrollChangeListener = listener
    }


}