package com.sohuott.tv.vod.videodetail.activity

import XCrashApp
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.Bundle
import android.os.CountDownTimer
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.view.KeyEvent
import android.view.View
import android.view.ViewTreeObserver
import android.view.WindowManager
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.UniqueOnly
import com.airbnb.mvrx.viewModel
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.lib_dlna_core.SohuDlnaManger
import com.lib_dlna_core.center.DlnaMediaModel
import com.lib_statistical.CLICK
import com.lib_statistical.IMP
import com.lib_statistical.addPushEvent
import com.lib_statistical.getInfoEvent
import com.lib_statistical.manager.RequestManager
import com.lib_statistical.model.EventInfo
import com.lib_viewbind_ext.viewBinding
import com.sh.ott.video.ShPlayerConfig
import com.sh.ott.video.ad.AdTsManger
import com.sh.ott.video.base.OnProgressChangedListener
import com.sh.ott.video.base.OnSkipStartAndEndListener
import com.sh.ott.video.base.ResolutionServer
import com.sh.ott.video.base.component.Resolution
import com.sh.ott.video.base.component.ShDataSource
import com.sh.ott.video.contor.IShComponentVisibilityListener
import com.sh.ott.video.contor.ShControlComponent
import com.sh.ott.video.contor.ShVideoViewController
import com.sh.ott.video.player.PlayerConfig
import com.sh.ott.video.player.PlayerConstants
import com.sh.ott.video.player.PlayerLogger
import com.sh.ott.video.player.base.OnStateChangeListener
import com.sh.ott.video.player.base.SystemPlayerFactory
import com.sh.ott.video.player.render.RenderFactory
import com.sh.ott.video.player.render.SurfaceViewRenderFactory
import com.sh.ott.video.player.sofa.SofaDrmBean
import com.sh.ott.video.player.sofa.SofaOptionModel
import com.sh.ott.video.player.sofa.SofaPlayerConfig
import com.sh.ott.video.player.sofa.SofaPlayerFactory
import com.sh.ott.video.player.util.PlayerException
import com.sh.ott.video.util.VideoStartTimeUtil
import com.sh.ott.video.vv.VvConfig
import com.sh.ott.video.vv.VvPushManger
import com.sohu.lib_utils.MetaDataHelper
import com.sohu.ott.base.lib_user.UserConfigHelper
import com.sohu.ott.base.lib_user.UserConstants
import com.sohu.ott.base.lib_user.UserInfoHelper
import com.sohu.ott.base.lib_user.UserLoginHelper
import com.sohu.ott.lib_widget.SoHuVideoProgressBar
import com.sohu.sofa.sofaplayer_java.SofaDataSource
import com.sohu.sofa.sofaplayer_java.SofaMediaPlayer
import com.sohuott.tv.vod.AppLogger
import com.sohuott.tv.vod.GlideApp
import com.sohuott.tv.vod.R
import com.sohuott.tv.vod.activity.PayActivity
import com.sohuott.tv.vod.activity.base.gone
import com.sohuott.tv.vod.activity.base.invisible
import com.sohuott.tv.vod.activity.base.isGone
import com.sohuott.tv.vod.activity.base.isInvisible
import com.sohuott.tv.vod.activity.base.isVisible
import com.sohuott.tv.vod.activity.base.scaleXY
import com.sohuott.tv.vod.activity.base.visible
import com.sohuott.tv.vod.activity.setting.play.PlaySettingHelper
import com.sohuott.tv.vod.activity.teenagers.TeenagersManger
import com.sohuott.tv.vod.app.config.AppConfigDatabase
import com.sohuott.tv.vod.app.config.ResolutionInfo
import com.sohuott.tv.vod.base_router.RouterConstants
import com.sohuott.tv.vod.base_router.RouterPath
import com.sohuott.tv.vod.data.HomeData
import com.sohuott.tv.vod.databinding.ActivityVideoBinding
import com.sohuott.tv.vod.lib.base.ActivityManagerUtil
import com.sohuott.tv.vod.lib.db.greendao.Collection
import com.sohuott.tv.vod.lib.db.greendao.PlayHistory
import com.sohuott.tv.vod.lib.push.event.LoginSuccessEvent
import com.sohuott.tv.vod.lib.push.event.LogoutEvent
import com.sohuott.tv.vod.lib.service.PlayHistoryService
import com.sohuott.tv.vod.lib.service.PlayHistoryService.PlayHistoryListener
import com.sohuott.tv.vod.lib.service.ThirdPartyService
import com.sohuott.tv.vod.lib.utils.Constant
import com.sohuott.tv.vod.lib.utils.ToastUtils
import com.sohuott.tv.vod.lib.utils.Util
import com.sohuott.tv.vod.lib.utils.Util.getHttpsParams
import com.sohuott.tv.vod.ui.EpisodeLayoutNew
import com.sohuott.tv.vod.utils.ActivityLauncher
import com.sohuott.tv.vod.utils.CollectionRecordHelper
import com.sohuott.tv.vod.utils.ParamConstant
import com.sohuott.tv.vod.utils.SouthMediaUtil
import com.sohuott.tv.vod.videodetail.VideoDetailRequestManager
import com.sohuott.tv.vod.videodetail.activity.control.IControlComponentHide
import com.sohuott.tv.vod.videodetail.activity.control.VideoAdHeaderComponent
import com.sohuott.tv.vod.videodetail.activity.control.VideoAdPauseFilmComponent
import com.sohuott.tv.vod.videodetail.activity.control.VideoAdPauseImageComponent
import com.sohuott.tv.vod.videodetail.activity.control.VideoBufferLoadComponent
import com.sohuott.tv.vod.videodetail.activity.control.VideoFLogoComponent
import com.sohuott.tv.vod.videodetail.activity.control.VideoHdrDetailComponent
import com.sohuott.tv.vod.videodetail.activity.control.VideoHuaPingComponent
import com.sohuott.tv.vod.videodetail.activity.control.VideoLogoComponent
import com.sohuott.tv.vod.videodetail.activity.control.VideoNextPlayPromptComponent
import com.sohuott.tv.vod.videodetail.activity.control.VideoNormalProgressComponent
import com.sohuott.tv.vod.videodetail.activity.control.VideoPlayControlComponent
import com.sohuott.tv.vod.videodetail.activity.control.VideoPlayMenuConfigComponent
import com.sohuott.tv.vod.videodetail.activity.control.VideoPromptCopyComponent
import com.sohuott.tv.vod.videodetail.activity.control.VideoPromptCopyComponent.Companion.HINT_TEXT_DEFAULT
import com.sohuott.tv.vod.videodetail.activity.control.VideoPromptCopyComponent.Companion.HINT_TEXT_DLNA_PERMISSION_KEY_FAIL
import com.sohuott.tv.vod.videodetail.activity.control.VideoPromptCopyComponent.Companion.HINT_TEXT_DRM_ERROR
import com.sohuott.tv.vod.videodetail.activity.control.VideoPromptCopyComponent.Companion.HINT_TEXT_ERROR
import com.sohuott.tv.vod.videodetail.activity.control.VideoPromptCopyComponent.Companion.HINT_TEXT_ERROR_OFFLINE
import com.sohuott.tv.vod.videodetail.activity.control.VideoPromptCopyComponent.Companion.HINT_TEXT_FINISH
import com.sohuott.tv.vod.videodetail.activity.control.VideoPromptCopyComponent.Companion.HINT_TEXT_NAN_CHUAN
import com.sohuott.tv.vod.videodetail.activity.control.VideoPromptCopyComponent.Companion.HINT_TEXT_NORMAL
import com.sohuott.tv.vod.videodetail.activity.control.VideoPromptCopyComponent.Companion.HINT_TEXT_TRY_FINISH
import com.sohuott.tv.vod.videodetail.activity.control.VideoStartPreparingComponent
import com.sohuott.tv.vod.videodetail.activity.control.VideoTrySeeComponent
import com.sohuott.tv.vod.videodetail.activity.service.EpisodeService
import com.sohuott.tv.vod.videodetail.activity.service.EpisodeServiceManger
import com.sohuott.tv.vod.videodetail.activity.service.VideoDetailService
import com.sohuott.tv.vod.videodetail.activity.service.VideoDetailServiceManger
import com.sohuott.tv.vod.videodetail.activity.state.FEE_ALBUM
import com.sohuott.tv.vod.videodetail.activity.state.FEE_FREE
import com.sohuott.tv.vod.videodetail.activity.state.FEE_VIDEO
import com.sohuott.tv.vod.videodetail.activity.state.ResolutionApp
import com.sohuott.tv.vod.videodetail.activity.state.SOUTHMEDIA_CHECK_FAIL
import com.sohuott.tv.vod.videodetail.activity.state.TrySeeData
import com.sohuott.tv.vod.videodetail.activity.state.VideoDetailState
import com.sohuott.tv.vod.videodetail.activity.state.VideoDetailViewResult
import com.sohuott.tv.vod.videodetail.activity.state.VideoInfoResponse
import com.sohuott.tv.vod.videodetail.activity.state.VideoPayInfoResponse
import com.sohuott.tv.vod.videodetail.activity.state.VideoPlayInfoResponse
import com.sohuott.tv.vod.videodetail.activity.state.VideoPlayResolutionInfo
import com.sohuott.tv.vod.videodetail.activity.viewmodel.VideoDetailMviViewModel
import com.sohuott.tv.vod.videodetail.data.model.VideoDetailRecommendModel
import com.sohuott.tv.vod.view.scalemenu.bean.ScaleContentOnlySeeMenuItem
import com.sohuott.tv.vod.view.scalemenu.bean.VideoInfoOnlySeeItem
import com.sohuott.tv.vod.widget.CornerTagImageView
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import java.text.SimpleDateFormat
import java.util.Date


@Route(path = RouterPath.Detail.VIDEO_DETAIL_ACTIVITY)
open class VideoActivity : AppCompatActivity(), MavericksView, View.OnFocusChangeListener,
    VideoPlayMenuConfigComponent.PlayMenuConfigListener, EpisodeService,
    VideoDetailVListViewNew.OnListScrollChangeListener,
    CollectionRecordHelper.ICollectionRecordListener, VideoDetailService, IControlComponentHide,
    IShComponentVisibilityListener {
    private val TAG = "VideoActivity"

    private val mViewBinding: ActivityVideoBinding by viewBinding(
        ActivityVideoBinding::bind
    )

    private val mVideoDetailViewModel: VideoDetailMviViewModel by viewModel()

    //支付信息
    private var mVideoPayInfoResponse: VideoPayInfoResponse? = null

    //影片专辑信息
    private var mVideoInfoResponse: VideoInfoResponse? = null

    //影片播放信息
    private var mVideoPlayInfoResponse: VideoPlayInfoResponse? = null

    //影片 LOGO 组件
    private var mVideoLogoComponent: VideoLogoComponent? = null

    //花屏展示
    private var mVideoHuaPingComponent: VideoHuaPingComponent? = null

    //影片试看组件
    private var mVideoTrySeeComponent: VideoTrySeeComponent? = null

    //提示文案组件
    private var mVideoPromptCopyComponent: VideoPromptCopyComponent? = null

    //播放控制组件
    private var mVideoPlayControlComponent: VideoPlayControlComponent? = null

    //暂停广告图片展示组件
    private var mVideoAdPauseImageComponent: VideoAdPauseImageComponent? = null

    //暂停广告 max 视频展示组件
    private var mVideoAdPauseFilmComponent: VideoAdPauseFilmComponent? = null

    //hdr 解释提示组件
    private var mVideoHdrDetailComponent: VideoHdrDetailComponent? = null

    //缓冲进度时展示组件
    private var mVideoBufferLoadComponent: VideoBufferLoadComponent? = null

    //小窗正常进度条组件
    private var mVideoNormalProgressComponent: VideoNormalProgressComponent? = null

    //播放菜单清晰度 选集等控制组件
    private var mVideoPlayMenuConfigComponent: VideoPlayMenuConfigComponent? = null

    //前贴广告展示组件
    private var mVideoAdHeaderComponent: VideoAdHeaderComponent? = null

    //下一集提示组件
    private var mVideoNextPlayPromptComponent: VideoNextPlayPromptComponent? = null

    //广告视频准备中提示组件
    private var mVideoStartPreparingComponent: VideoStartPreparingComponent? = null

    private var mVideoFLogoComponent: VideoFLogoComponent? = null

    //下一集信息
    private var nextEpisodeInfo: MutableList<Any> = mutableListOf<Any>()


    private val mPaySourceComeFromType = PayActivity.PAY_SOURCE_DETAIL

    private val partnerNo = MetaDataHelper.getPartnerNo()
    private val hasLogin: Boolean
        get() = if (isDlna) mDlnaMode?.isLogin ?: false else UserLoginHelper.getInstants()
            .getIsLogin()
    private val loginToken: String?
        get() = UserLoginHelper.getInstants().getLoginToken()
    private val gid: String
        get() = if (hasLogin) UserInfoHelper.getGid() ?: "" else ""
    private val passport: String
        get() = if (hasLogin) UserLoginHelper.getInstants().getLoginPassport()
            ?: "" else UserInfoHelper.getGid() ?: ""

    private val hasVip: Boolean
        get() = if (isDlna) mDlnaMode?.isVip ?: false else UserLoginHelper.getInstants().isVip()
    private val vipTime: String?
        get() = UserLoginHelper.getInstants().getVipTime()

    private val hasTeenager: Boolean
        get() = TeenagersManger.isTeenager()

    private var aid = 0
    private var vid = 0
    private var sourceId = 0
    private var dataType = 0
    private var fromPage = 0
    private var backHome = 0
    private var pDna = ""
    private var thirdPlayTime = -1
    private var isDts = false
    private var tvAreaId: Int = 0
    private var tvCateCode: Int = 0
    private var tvCateId: Int = 0
    private var normalVideoFocus: View? = null
    private var mResolutionApps = mutableMapOf<ResolutionApp, VideoPlayResolutionInfo>()

    private var hasEpisode = false

    private var hasTrailer = false

    //剩余时间倒计时
    private var mMyCountDownTimer: MyCountDownTimer? = null

    private var mVideoDetailRecommendModel: VideoDetailRecommendModel? = null

    //会员海报 initApp 配置信息
    val configInfo = HomeData.getLocationConfigInfo(null)

    //播放状态
    var filmPlayState = PlayerConstants.VideoState.IDLE
    var adPlayState = PlayerConstants.VideoState.IDLE

    //包含广告和正片 不做区分
    var videoPlayState = PlayerConstants.VideoState.IDLE

    /**
     * 播放器窗口类型
     */
    val isFull: Boolean
        get() = screenMode == PlayerConstants.ScreenMode.FULL
    var screenMode: Int = PlayerConstants.ScreenMode.NORMAL
    private val windowFloat: Boolean
        get() = screenMode == PlayerConstants.ScreenMode.TINY
    val isNormal: Boolean
        get() = screenMode == PlayerConstants.ScreenMode.NORMAL

    /**
     * 播放器播放信息
     */
    private var mShDataSource = ShDataSource()
    private var mBaseOptionModel = SofaOptionModel()

    private lateinit var mCollectionHelper: CollectionRecordHelper

    /**
     * 收藏状态
     */
    private var enableCollect = false

    /**
     * 教育会员
     */
    private var mEduVip = false

    /**
     * 影片类型
     */
    private var feeType = FEE_FREE

    /**
     * 是否是片花Tab
     */
    private var isTrailerTab = false


    /**
     * 会员相关
     */
    private var isTicket = false
    private var isMember = false
    private var isSingle = false
    private var ticketCount = 0

    /**
     * 视频总时长
     */
    private var mDuration = 0L

    /*
    * 当前播放位置
     */
    private var mPosition = 0L

    private var mCurrentUrl: String? = null

    /**
     * 片尾时间
     */
    private var mTvEndTime = 0L
    private var mTvStartTime = 0L

    private lateinit var playHistoryService: PlayHistoryService

    /**
     * 跳转支付页逻辑判断
     */
    private var startPayActivity = true

    /**
     *  0: 不需要检查；1:需要检查&重播；2:需要检查&不需要重播
     */
    private var resumeUserStatus: Int = 0

    /**
     * 当前用户的Passport
     */
    private var mInitPassport: String? = null

    private var isResume = false
        get() {
            return field && (!mViewBinding.detailLoadingView.isVisible || !mViewBinding.detailErrorLayout.errView.isVisible)
        }

    /**
     * 重新进入需要展示加载界面
     */
    private var reInitView = false


    /**
     * 当前视频历史记录的观看时间
     */
    private var currentHistoryWatchTime = 0L

    private var isReplay = false


    /**
     * dlna 投屏信息
     */
    @JvmField
    @Autowired
    var mDlnaMode: DlnaMediaModel? = null

    /**
     * 是否来源于搜狐视频投屏
     */
    private val isShDlnaVideo
        get() = SohuDlnaManger.getInstance().getIsSohuVideo()

    //投屏
    private var isDlna = false
        get() = SohuDlnaManger.getInstance().getIsDlna()

    @JvmField
    @Autowired
    var isAllFull = false


    override fun onCreate(savedInstanceState: Bundle?) {
        ARouter.getInstance().inject(this)
        super.onCreate(savedInstanceState)
        //6.0下部分机型屏幕闪烁
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            window.setFlags(
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
            )
        }

        playHistoryService = PlayHistoryService(this.applicationContext)
        AppLogger.d(TAG, "onCreate")
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        setContentView(R.layout.activity_video)
        initData(intent)
        initVideo()

        observeData()

        AppLogger.d("播放版本信息 sh-video info:${ShPlayerConfig.getBuildInfo()}   sofa info :${SofaMediaPlayer.getVersion()}")
    }

    private fun observeData() {
        // 保持请求-回调流程，但简化观察者，参考VideoActivity(2).kt
        mVideoDetailViewModel.onAsync(VideoDetailState::videoInfoData, onSuccess = {
            // 请求成功后设置URL并播放
            mShDataSource.url =
                "http://hotvrs.vod.ystyt.aisee.tv/m3u8v3/9542778_4965873495981_2753099.m3u8?plat=15&pt=6&prod=ott&pg=1&ch=v&qd=9999&cv=7.4.0.1&uid=02ffff107400006b6aa400bf7299afcef5c1cb7fff11b8&player=2.0&p_type=2"
            setVideoOption()
            prepareVideo()
        }, onFail = {
            // 处理失败情况
        })

        // 发起请求
        mVideoDetailViewModel.requestVideo(
            dataType,
            aid = aid,
            vid = vid,
            false,
            isChangeVideo = false
        )
    }


    override fun onResume() {
        super.onResume()

    }

    override fun onStop() {
        super.onStop()
        GlideApp.with(this).onStop()
        AppLogger.d(TAG, "onStop")
    }

    override fun onPause() {
        super.onPause()
        AppLogger.d(TAG, "onPause")
        GlideApp.with(this).pauseRequests()
        mViewBinding.detailShVideo.pause()
    }

    private var screenStateReceiver: ScreenStateReceiver? = null
    override fun onDestroy() {
        super.onDestroy()
        onVvClose()
        mAllOnStateChangeListener = null
        mFilmOnStateChangeListener = null
        mAdOnStateChangeListener = null
        mViewBinding.detailShVideo.destroy()
        AppLogger.d(TAG, "onDestroy")
        isScreenOFF = false
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)

    }

    /**
     * 可见时鉴权
     */
    private fun resumeLoadUserStatus() {
        AppLogger.d(TAG, "isReplay:$isReplay resumeUserStatus:$resumeUserStatus")
        if (isReplay) {
            if (resumeUserStatus != 0 && !mInitPassport.equals(passport)) {
                requestPayInfo()
                if (resumeUserStatus == 2 && promptCopyComponentHasShow() == false) {
                    replayPayAfterVideo()
                    return
                }
                mViewBinding.detailShVideo.resume()
            } else if (resumeUserStatus != 0) {
                requestPayInfo()
                if (resumeUserStatus == 2 && promptCopyComponentHasShow() == false) {
                    replayPayAfterVideo()
                    return
                }
                mViewBinding.detailShVideo.resume()
            } else if (!mInitPassport.equals(passport)) {
                requestPayInfo()
                replayPayAfterVideo()
                return
            }
            if (promptCopyComponentHasShow() == true) {
                return
            }
            replayPayAfterVideo()
        } else {
            mViewBinding.detailShVideo.resume()
        }
    }


    /**
     * 初始化加载界面
     */
    private fun initViewLoadState() {
        mViewBinding.detailLoadingView.show()
        mViewBinding.detailRecommendList.gone()
        mViewBinding.detailShHeaderLayout.gone()
        mViewBinding.detailShVideo.gone()
        mViewBinding.detailErrorLayout.errView.gone()
        onScrollWindowChange(false)
        AppLogger.d(TAG, "1 onScrollWindowChange(false)")
    }

    /**
     * 获取外部传递信息
     */
    private fun initData(intent: Intent?) {
        // 实验1: 注释掉initViewLoadState()调用，测试是否解决闪烁
        // initViewLoadState()
        thirdPlayTime =
            intent?.getIntExtra(RouterConstants.VideoDetailParamsKey.THIRD_PLAY_TIME, -1)
                ?: -1

        if (isScreenOFF) {
            isScreenOFF = false
            return
        }
        if (intent?.action?.equals("com.sohuott.tv.vod.action.DETAIL") == true) {
            aid = intent?.getIntExtra(RouterConstants.VideoDetailParamsKey.AID, 0) ?: 0
            vid = intent?.getIntExtra(RouterConstants.VideoDetailParamsKey.VID, 0) ?: 0
            dataType =
                intent?.getIntExtra(RouterConstants.VideoDetailParamsKey.VIDEO_TYPE, 0) ?: 0
            fromPage = intent?.getIntExtra(
                RouterConstants.VideoDetailParamsKey.NEW_VIDEO_DETAIL_FROM,
                0
            ) ?: 0
            isDts = intent?.getBooleanExtra(RouterConstants.VideoDetailParamsKey.IS_DTS, false)
                ?: false
            return
        }
        if (intent?.scheme?.contains("yt://sohu.tv/videodetail?") == true || intent?.data != null) {
            aid =
                intent?.data?.getQueryParameter(RouterConstants.VideoDetailParamsKey.AID)?.toInt()
                    ?: 0
            vid = intent?.data?.getQueryParameter(RouterConstants.VideoDetailParamsKey.VID)
                ?.toInt() ?: 0
            sourceId =
                intent?.data?.getQueryParameter(RouterConstants.VideoDetailParamsKey.SOURCE_ID)
                    ?.toInt()
                    ?: 0
            dataType =
                intent?.data?.getQueryParameter(RouterConstants.VideoDetailParamsKey.TYPE)
                    ?.toInt() ?: 0
            fromPage =
                intent?.data?.getQueryParameter(RouterConstants.VideoDetailParamsKey.PAGE_SOURCE)
                    ?.toInt() ?: 0

            isDts = ((intent?.data?.getQueryParameter(RouterConstants.VideoDetailParamsKey.DTS)
                ?.toInt() ?: 0) == 1
                    )

            backHome =
                intent?.data?.getQueryParameter(RouterConstants.VideoDetailParamsKey.BACK_HOME)
                    ?.toInt()
                    ?: 0
            return
        }
        val aidValue = intent?.extras?.get(RouterConstants.VideoDetailParamsKey.AID)
        if (aidValue != null) {
            aid = aidValue.toString().toInt()
        } else {
            aid = 0
        }
        val vidValue = intent?.extras?.get(RouterConstants.VideoDetailParamsKey.VID)
        if (vidValue != null) {
            vid = vidValue.toString().toInt()
        } else {
            vid = 0
        }
        dataType =
            intent?.getStringExtra(RouterConstants.VideoDetailParamsKey.TYPE)?.toInt() ?: 0

        isDts = (intent?.getStringExtra(RouterConstants.VideoDetailParamsKey.DTS)?.toInt()
            ?: 0) == 1
        fromPage =
            intent?.getStringExtra(RouterConstants.VideoDetailParamsKey.PAGE_SOURCE)?.toInt()
                ?: 0
        sourceId =
            intent?.getStringExtra(RouterConstants.VideoDetailParamsKey.SOURCE_ID)?.toInt() ?: 0
        backHome =
            intent?.getStringExtra(RouterConstants.VideoDetailParamsKey.BACK_HOME)?.toInt() ?: 0
        pDna = intent?.getStringExtra(RouterConstants.VideoDetailParamsKey.P_DNA) ?: ""
    }

    private var mAllOnStateChangeListener: AllOnStateChangeListener? = null
    private var mAdOnStateChangeListener: AdOnStateChangeListener? = null
    private var mFilmOnStateChangeListener: FilmOnStateChangeListener? = null

    /**
     * 初始化 video 相关数据
     */
    private fun initVideo() {
        onlySeeIndex = 0
        mViewBinding.detailShVideo.setFilmPlayerFactory(if (Util.getPlayParams(this) == 0) SofaPlayerFactory.create() else SystemPlayerFactory.create())
        //创建和添加播放需要展示的组件
        //设置正片的画面比例
        mViewBinding.detailShVideo.setFilmScreenAspectRatioType(PlaySettingHelper.getVideoViewLayoutRatioType())
        //       添加正片进度条监听
        mViewBinding.detailShVideo.addFilmProgressChangedListener(object :
            OnProgressChangedListener {
            override fun onVideoProgressChanged(duration: Long, position: Long) {
                mDuration = duration / 1000
                mPosition = position / 1000
                if (SohuDlnaManger.getInstance().getIsDlna()) {
                    SohuDlnaManger.getInstance().sendPlayTime(position.toInt(), duration.toInt())
                }
            }
        })
        mAllOnStateChangeListener = AllOnStateChangeListener()
        //添加正片和广告的状态监听
        mViewBinding.detailShVideo.addOnStateChangeListener(mAllOnStateChangeListener!!)
        mAdOnStateChangeListener = AdOnStateChangeListener()
        //添加广告的状态监听
        mViewBinding.detailShVideo.addAdOnStateChangeListener(mAdOnStateChangeListener!!)
        mFilmOnStateChangeListener = FilmOnStateChangeListener()
        //添加正片的状态监听
        mViewBinding.detailShVideo.addFilmOnStateChangeListener(mFilmOnStateChangeListener!!)

    }

    //跳过片尾提示
    private fun skipEnd() {
        ToastUtils.showToast(this@VideoActivity, "已为您跳过片尾！")

    }

    // 跳过片头提示
    private fun skipStart() {
        ToastUtils.showToast(this@VideoActivity, "已为您跳过片头！")

    }

    /**
     * 判断提示文案是否显示
     */
    private fun promptCopyComponentHasShow(): Boolean {
        return mVideoPromptCopyComponent?.hasShow() == true
    }

    /**
     * dlna 搜狐视频来源鉴权文案是否显示
     */
    private fun hasShowDlnaPermissionView(): Boolean {
        return mVideoPromptCopyComponent?.getHasShowDlnaPermissionView() == true
    }

    /**
     * 设置组件需要的会员信息
     */
    private fun setPayHintMemberPayInfo() {
        mVideoTrySeeComponent?.setMemberPayInfo(isSingle, isMember, isTicket, ticketCount)
        mVideoPromptCopyComponent?.setMemberPayInfo(isSingle, isMember, isTicket, ticketCount)
        if (mVideoPromptCopyComponent?.hasShow() == true) {
            mVideoPromptCopyComponent?.refreshPayHintText()
        }
    }

    /**
     * 设置需要提示文案需要的影片付费类型
     */
    private fun setPayHintFeeType() {
        mVideoPromptCopyComponent?.feeType = feeType
    }

    /**
     * 设置提示文案类型
     */
    private fun setPayHintText(int: Int) {
        mVideoPromptCopyComponent?.setPayHintText(int)
        //组件展示冲突需要隐藏
        if (mVideoPromptCopyComponent?.hasShow() == true) {
            mVideoStartPreparingComponent?.hide()
        }
    }

    /**
     * 停止进度条更新
     */
    private fun stopUpdateProgress() {
        mViewBinding.detailShVideo.getFilmVideoController()?.stopUpdateProgress()
    }

    /**
     * 开启进度条更新
     */
    private fun startUpdateProgress() {
        mViewBinding.detailShVideo.getFilmVideoController()?.startUpdateProgress()
    }

    /**
     * 是否是 试看结束
     */
    private fun isTryFinish(): Boolean {
        if (isDlna) {
            return false
        }
        if (isTryVideo) {
            if (isFull) {
                startVideoWindowNormal()
            }
            saveHistory()
            setPayHintText(HINT_TEXT_TRY_FINISH)
            onVvComplete()
            return true
        }
        return false
    }

    private fun onPlayCompleted(reason: Int) {
        if (reason != 1) {
            //0: 最后一集
            //1.判断当前是否是选集tab 判断是否有花絮tab
            //3.请求第一集花絮获取id
            //4.播放第一集花絮
            if (dataType != Constant.DATA_TYPE_VRS) {
                return
            }
            if (tvCateCode == Constant.CATECODE_MOVIE) {
                return
            }
            if (!isTrailerTab && hasTrailer) {
                mVideoDetailViewModel.getNextTrailerEpisodeInfo(mVideoInfoResponse?.trailerId ?: 0)
            } else {
                startVideoWindowNormal()
            }
        }
    }

    private fun showPayOrTicketDialog() {
        if (mVideoPayInfoResponse?.playRequire?.isEmpty() == true) {
            setJumpToPayStatus()
            startPayActivity = false
            ActivityLauncher.startPayActivity(this, mPaySourceComeFromType)
            return
        }
        if (hasLogin) {
            if (hasVip && (System.currentTimeMillis()) <= (vipTime?.toLong() ?: 0L)) {
                if (isTicket && !isMember && !isSingle) {
                    if (ticketCount > 0) {
                        setJumpToPayStatus()
                        startPayActivity = false
                        ActivityLauncher.startTicketUseActivity(
                            this,
                            if (mVideoPlayInfoResponse?.isExtendNull != null) mVideoPlayInfoResponse?.albumTVName else mVideoPlayInfoResponse?.videoTvName,
                            if (mVideoPlayInfoResponse?.isExtendNull != null && !TextUtils.isEmpty(
                                    mVideoPlayInfoResponse?.tvVerPic
                                )
                            ) mVideoPlayInfoResponse?.tvVerPic else mVideoPlayInfoResponse?.videoExtendsPic,
                            aid.toLong(),
                            vid.toLong()
                        )
                    } else {
                        setJumpToPayStatus()
                        startPayActivity = false

                        //续费会员，立即赠券。本片因版权方要求，需要付费观看。
                        ActivityLauncher.startPayActivity(
                            this,
                            if (mVideoPlayInfoResponse?.isExtendNull != null && !TextUtils.isEmpty(
                                    mVideoPlayInfoResponse?.tvVerPic
                                )
                            ) mVideoPlayInfoResponse?.tvVerPic else mVideoPlayInfoResponse?.videoExtendsPic,
                            aid,
                            vid,
                            mPaySourceComeFromType
                        )
                    }
                } else if (isTicket && isSingle && !isMember) {
                    if (ticketCount > 0) {
                        setJumpToPayStatus()
                        startPayActivity = false

                        ActivityLauncher.startTicketUseActivity(
                            this,
                            if (mVideoPlayInfoResponse?.isExtendNull != null) mVideoPlayInfoResponse?.albumTVName else mVideoPlayInfoResponse?.videoTvName,
                            if (mVideoPlayInfoResponse?.isExtendNull != null && !TextUtils.isEmpty(
                                    mVideoPlayInfoResponse?.tvVerPic
                                )
                            ) mVideoPlayInfoResponse?.tvVerPic else mVideoPlayInfoResponse?.videoExtendsPic,
                            aid.toLong(),
                            vid.toLong()
                        )
                    } else {
                        setJumpToPayStatus()
                        startPayActivity = false

                        //续费会员，立即赠券。本片因版权方要求，需要付费观看。
                        ActivityLauncher.startPayActivity(
                            this,
                            if (mVideoPlayInfoResponse?.isExtendNull != null && !TextUtils.isEmpty(
                                    mVideoPlayInfoResponse?.tvVerPic
                                )
                            ) mVideoPlayInfoResponse?.tvVerPic else mVideoPlayInfoResponse?.videoExtendsPic,
                            aid,
                            vid,
                            mPaySourceComeFromType
                        )
                    }
                } else if (isSingle && !isMember && !isTicket) {
                    setJumpToPayStatus()
                    startPayActivity = false
                    ActivityLauncher.startPayActivity(
                        this,
                        aid,
                        vid,
                        mVideoPlayInfoResponse?.albumTVName,
                        if (mVideoPlayInfoResponse?.isExtendNull != null && !TextUtils.isEmpty(
                                mVideoPlayInfoResponse?.tvVerPic
                            )
                        ) mVideoPlayInfoResponse?.tvVerPic else mVideoPlayInfoResponse?.videoExtendsPic,
                        1,
                        mPaySourceComeFromType,
                        if (mVideoPlayInfoResponse?.cateId == 21) 21 else 0
                    )
                } else {
                    setJumpToPayStatus()
                    startPayActivity = false

                    ActivityLauncher.startPayActivity(
                        this,
                        ParamConstant.PAGE_SOURCE_PLAYER,
                        mPaySourceComeFromType,
                        false
                    )
                }
            } else {
                if (isTicket && isSingle && !isMember) {
                    setJumpToPayStatus()
                    startPayActivity = false
                    ActivityLauncher.startPayActivity(
                        this,
                        aid,
                        vid,
                        mVideoPlayInfoResponse?.albumTVName,
                        if (mVideoPlayInfoResponse?.isExtendNull != null && !TextUtils.isEmpty(
                                mVideoPlayInfoResponse?.tvVerPic
                            )
                        ) mVideoPlayInfoResponse?.tvVerPic else mVideoPlayInfoResponse?.videoExtendsPic,
                        2,
                        mPaySourceComeFromType,
                        0
                    )
                } else if (isSingle && !isMember && !isTicket) {
                    setJumpToPayStatus()
                    startPayActivity = false

                    ActivityLauncher.startPayActivity(
                        this,
                        aid,
                        vid,
                        mVideoPlayInfoResponse?.albumTVName,
                        if (mVideoPlayInfoResponse?.isExtendNull != null && !TextUtils.isEmpty(
                                mVideoPlayInfoResponse?.tvVerPic
                            )
                        ) mVideoPlayInfoResponse?.tvVerPic else mVideoPlayInfoResponse?.videoExtendsPic,
                        1,
                        mPaySourceComeFromType,
                        if (mVideoPlayInfoResponse?.cateId == 21) 21 else 0
                    )

                } else if (!isTicket && isSingle && isMember) {
                    setJumpToPayStatus()

                    startPayActivity = false

                    ActivityLauncher.startPayActivity(
                        this,
                        aid,
                        vid,
                        mVideoPlayInfoResponse?.albumTVName,
                        if (mVideoPlayInfoResponse?.isExtendNull != null && !TextUtils.isEmpty(
                                mVideoPlayInfoResponse?.tvVerPic
                            )
                        ) mVideoPlayInfoResponse?.tvVerPic else mVideoPlayInfoResponse?.videoExtendsPic,
                        2,
                        mPaySourceComeFromType,
                        0
                    )
                } else if (!isTicket && !isSingle && isMember) {
                    setJumpToPayStatus()
                    startPayActivity = false

                    ActivityLauncher.startPayActivityWithAidVid(
                        this,
                        aid,
                        vid,
                        ParamConstant.PAGE_SOURCE_PLAYER,
                        mPaySourceComeFromType,
                        false
                    )
                } else {
                    setJumpToPayStatus()

                    startPayActivity = false

                    ActivityLauncher.startPayActivity(
                        this,
                        ParamConstant.PAGE_SOURCE_PLAYER,
                        mPaySourceComeFromType,
                        false
                    )
                }

            }

        } else {
            if (isTicket && !isSingle && !isMember) {
                setJumpToPayStatus()

                //加入会员，立即赠券。本片因版权方要求，需要付费观看。
                startPayActivity = false

                ActivityLauncher.startPayActivity(
                    this,
                    ParamConstant.PAGE_SOURCE_PLAYER,
                    mPaySourceComeFromType,
                    false
                )
            } else if (isSingle && !isTicket && !isMember) {
                setJumpToPayStatus()

                startPayActivity = false

                ActivityLauncher.startPayActivity(
                    this,
                    aid,
                    vid,
                    mVideoPlayInfoResponse?.albumTVName,
                    if (mVideoPlayInfoResponse?.isExtendNull != null && !TextUtils.isEmpty(
                            mVideoPlayInfoResponse?.tvVerPic
                        )
                    ) mVideoPlayInfoResponse?.tvVerPic else mVideoPlayInfoResponse?.videoExtendsPic,
                    1,
                    mPaySourceComeFromType,
                    if (mVideoPlayInfoResponse?.cateId == 21) 21 else 0
                )
            } else if (isTicket && isSingle && !isMember) {
                setJumpToPayStatus()

                startPayActivity = false

                //加入会员，立即赠券。本片因版权方要求，需要付费观看。
                ActivityLauncher.startPayActivity(
                    this,
                    aid,
                    vid,
                    mVideoPlayInfoResponse?.albumTVName,
                    if (mVideoPlayInfoResponse?.isExtendNull != null && !TextUtils.isEmpty(
                            mVideoPlayInfoResponse?.tvVerPic
                        )
                    ) mVideoPlayInfoResponse?.tvVerPic else mVideoPlayInfoResponse?.videoExtendsPic,
                    2,
                    mPaySourceComeFromType,
                    0
                )

            } else if (isMember && isSingle && !isTicket) {
                setJumpToPayStatus()

                startPayActivity = false

                //本片因版权方要求，需要付费观看。
                ActivityLauncher.startPayActivity(
                    this,
                    aid,
                    vid,
                    mVideoPlayInfoResponse?.albumTVName,
                    if (mVideoPlayInfoResponse?.isExtendNull != null && !TextUtils.isEmpty(
                            mVideoPlayInfoResponse?.tvVerPic
                        )
                    ) mVideoPlayInfoResponse?.tvVerPic else mVideoPlayInfoResponse?.videoExtendsPic,
                    2,
                    mPaySourceComeFromType,
                    0
                )
            } else if (isMember && !isSingle && !isTicket) {
                setJumpToPayStatus()

                startPayActivity = false

                //本片因版权方要求，需要付费观看。
                ActivityLauncher.startPayActivityWithAidVid(
                    this,
                    aid,
                    vid,
                    ParamConstant.PAGE_SOURCE_PLAYER,
                    mPaySourceComeFromType,
                    false
                )

            } else {
                setJumpToPayStatus()
                startPayActivity = false
                ActivityLauncher.startPayActivityWithAidVid(
                    this,
                    aid,
                    vid,
                    ParamConstant.PAGE_SOURCE_PLAYER,
                    mPaySourceComeFromType,
                    false
                )
            }
        }

    }

    /**
     * 设置view的焦点监听
     */
    private fun setFocusChangeListener() {
        //简介
        mViewBinding.clScaleBriefIntroduction.onFocusChangeListener = this
        //全屏
        mViewBinding.btnVideoDetailHeaderFull.onFocusChangeListener = this
        //购买
        mViewBinding.btnVideoDetailHeaderPay.onFocusChangeListener = this
        //会员
        mViewBinding.btnVideoDetailHeaderMembers.onFocusChangeListener = this
        //收藏
        mViewBinding.btnVideoDetailHeaderCollection.onFocusChangeListener = this

        mViewBinding.detailRecommendList.setOnListScrollChangeListener(this)
        mViewBinding.detailShVideo.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                mViewBinding.fragmentItemFocus.visible()
                mViewBinding.fragmentItemFocus.setFocusView(mViewBinding.detailShVideo)
            } else {
                mViewBinding.fragmentItemFocus.gone()
                mViewBinding.fragmentItemFocus.clearFocus()
            }
        }

        mViewBinding.headerViewVideoDetailUser.setUserHeaderFocusChangeListener { hasFocus ->
            if (hasFocus && !isNormal) {
                AppLogger.d(
                    TAG,
                    "setUserHeaderFocusChangeListener windowFloat:${windowFloat}, isNormal:${isNormal}"
                )
                startVideoWindowNormal()
                updateHeaderWindow()
            }
        }
    }


    @Subscribe
    fun onEventMainThread(event: LoginSuccessEvent?) {
        //Receive EventBus about login and reset user image to custom picture
        AppLogger.d(TAG, "onEventMainThread(LoginSuccessEvent event)")

        if (event == null) {
            return
        }
        refreshTopBar()
    }


    @Subscribe
    fun onEventMainThread(event: LogoutEvent?) {
        //Receive EventBus about logout and reset user image to default
        AppLogger.d(TAG, "onEventMainThread(LogoutEvent event)")
        if (event == null) {
            return
        }
        refreshTopBar()
    }

    /**
     * 刷新用户栏数据
     */
    private fun refreshTopBar() {
        mViewBinding.headerViewVideoDetailUser.updateLoginDisplay()
        mVideoDetailViewModel.getTopData(
            passport = passport,
            token = loginToken
        )
    }

    /**
     * 请求支付信息数据
     */
    private fun requestPayInfo() {
        if (dataType == Constant.DATA_TYPE_VRS) {
            mVideoDetailViewModel.requestPayInfo(
                hasLogin,
                hasVip,
                tvCateCode ?: 0,
                passport = passport ?: "",
                mUserLoginToken = loginToken ?: "",
                aid = aid,
                vid = if (dataType == Constant.DATA_TYPE_VRS) vid else aid
            )
        }
    }


    /**
     * 付费鉴权需要拼接的KEY
     */
    private var mVideoPermissionKey: String? = ""

    /**
     * 是否付费鉴权成功
     */
    private var hasPermissionCheckSuccess = true

    /**
     * 初始化鉴权相关参数 恢复默认值
     */
    private fun initVideoPermissionParams() {
        southMediaSuccess = true
        mVideoPermissionKey = null
        hasPermissionCheckSuccess = true
    }

    var isTryVideo = false
    var tryTime: Long = 0L
    var tryTimeText: String? = null

    private var tvPlayType = 0

    /**
     * 刷新是否是试看
     */
    private fun refreshIsTryVideo() {
        /**
         * 是否是试看
         */
        if (isDlna) {
            isTryVideo = false
        } else if (dataType == Constant.DATA_TYPE_PGC) {
            isTryVideo = false
        } else if (dataType == Constant.DATA_TYPE_VRS) {
            if ((mVideoPlayInfoResponse?.isMemberPlay == 1 && feeType == FEE_VIDEO && !hasVip) || (feeType == FEE_ALBUM && !hasPermissionCheckSuccess)) {
                isTryVideo = true
                if (feeType == FEE_ALBUM) {
                    tryTime = 5 * 60
                    tryTimeText = "5"
                } else {
                    tryTime = mVideoPlayInfoResponse?.trySeeTime ?: 0
                    tryTimeText =
                        "${((mVideoPlayInfoResponse?.trySeeTime ?: 0) / 60).toInt()}"
                }
            } else {
                isTryVideo = false
            }

        } else {
            isTryVideo = false
        }
        AppLogger.d(TAG, "refreshIsTryVideo 是否是试看 isTryVideo：$isTryVideo")
        //试看 不播放drm
        if (isTryVideo) {
            if (getServiceTvPlayType() == 3) {
                tvPlayType = 0
                AppLogger.d(TAG, "refreshIsTryVideo 试看 tvPlayType 为 3 强制切换 0")
            }
        }
        setTryDate()
    }

    /**
     * 设置试看相关组件需要的数据信息
     */
    private fun setTryDate() {
        mVideoPlayControlComponent?.trySeeTime =
            tryTime
        mVideoPlayControlComponent?.isTryVideo =
            isTryVideo
        mVideoPlayMenuConfigComponent?.trySeeTime =
            tryTime
        mVideoPlayMenuConfigComponent?.isTryVideo =
            isTryVideo
        mVideoNormalProgressComponent?.trySeeTime =
            tryTime
        mVideoNormalProgressComponent?.isTryVideo =
            isTryVideo
        mVideoTrySeeComponent?.trySeeData = TrySeeData().also {
            it.enableTry = isTryVideo
            it.tryTime = tryTime
            it.tryTimeText = tryTimeText
        }
    }


    //南传鉴权是否成功
    private var southMediaSuccess = true

    /**
     * 南传鉴权请求
     */
    private fun southMediaCheck(type: Int) {
        mVideoDetailViewModel.southMediaCheckPermission(dataType, type, vid, getCurrentAid())
    }

    /**
     * 付费鉴权失败
     */
    private fun videoPermissionCheckFail() {
        AppLogger.d(TAG, "鉴权失败 videoPermissionCheckFail")
        mVideoPermissionKey = null
        hasPermissionCheckSuccess = false
        if (feeType == FEE_ALBUM) {
            getPlayHistoryByIdTime()
        } else if (feeType == FEE_VIDEO) {
            if (isDlna && isShDlnaVideo) {
                setPayHintText(HINT_TEXT_DLNA_PERMISSION_KEY_FAIL)
                return
            }
            //付费电视剧鉴权失败
            if (mVideoPlayInfoResponse?.cateId == 21) {
                if (isFull) {
                    startVideoWindowNormal()
                }
                setPayHintText(HINT_TEXT_NORMAL)
                setResumeUserStatus(2)
                ActivityLauncher.startPayActivity(
                    this,
                    aid,
                    mVideoPayInfoResponse?.feePayVid
                        ?: 0,
                    mVideoPlayInfoResponse?.videoTvName,
                    mVideoPlayInfoResponse?.tvVerPic,
                    1,
                    PayActivity.PAY_SOURCE_DETAIL,
                    21
                )
            } else if (startPayActivity) {
                if (isFull) {
                    startVideoWindowNormal()
                }
                setPayHintText(HINT_TEXT_NORMAL)
                setResumeUserStatus(2)
                ActivityLauncher.startPayActivity(this, mPaySourceComeFromType)
            }
            startPayActivity = true
        }

    }

    private fun observeUser() {
        //南传鉴权
        mVideoDetailViewModel.onAsync(
            VideoDetailState::southMediaCheckResult, onSuccess = {
                AppLogger.w(TAG, "onSouthMediaCheck state :$it")
                southMediaSuccess = it != SOUTHMEDIA_CHECK_FAIL
                if (!southMediaSuccess) {
                    setPayHintText(HINT_TEXT_NAN_CHUAN)
                    mViewBinding.detailShVideo.release(false, false)
                    AppLogger.w(TAG, "onSouthMediaCheckError")
                    return@onAsync
                }
                AppLogger.w(TAG, "onSouthMediaCheckSuccess")
            }
        )
        //视频鉴权
        mVideoDetailViewModel.onAsync(
            VideoDetailState::videoPermissionCheck, onSuccess = {
                if (it.data == null || TextUtils.isEmpty(it.data?.mkey)) {
                    startPayActivity = true
                    videoPermissionCheckFail()
                } else {
                    hasPermissionCheckSuccess = true
                    mVideoPermissionKey = it.data?.mkey!!
                    getPlayHistoryByIdTime()
                }
            }, onFail = {
                startPayActivity = true
                videoPermissionCheckFail()
            },
            deliveryMode = UniqueOnly("鉴权")
        )
        //用户栏信息
        mVideoDetailViewModel.onAsync(
            VideoDetailState::topData,
            deliveryMode = UniqueOnly("用户栏")
        ) {
            AppLogger.d(TAG, "topData Success :${it.toString()}")
            mViewBinding.headerViewVideoDetailUser.setData(it)
        }
    }

    private fun albumError(msg: String) {
        tvCateCode = 0
        mVideoInfoResponse = null
        mViewBinding.detailLoadingView.hide()
        mViewBinding.detailErrorLayout.errView.visible()
        mViewBinding.detailErrorLayout.errorHint.text = msg
        AppLogger.d(TAG, "VideoDetailViewResult Fail errorMessage: ${msg}")
    }

    private fun albumSuccess() {
        isTrailerTab = false
        getHistoryVid()
        showVideoView()
        requestCollect()
        mVideoHuaPingComponent?.setPlayerBackground(mVideoInfoResponse?.albumExtendsPic_640_360)
        mVideoInfoResponse?.apply {
            <EMAIL> = hasEpisode
            <EMAIL> = tvAreaId
            <EMAIL> = cateCode
            logoInfo = pgclogoInfo
            tvCateCode = cateCode
            mVideoPlayControlComponent?.recordNumber = recordNumber
            mVideoTrySeeComponent?.recordNumber = recordNumber
            mVideoPlayControlComponent?.videoLength = videoLength?.toLong() ?: 0
            mVideoNormalProgressComponent?.videoLength = videoLength?.toLong() ?: 0
            mVideoPlayControlComponent?.setTitle(videoName)
            //避免vrs 获取videoinfo之后产生 展示文案变化频繁造成闪烁效果
            if (dataType == Constant.DATA_TYPE_PGC) {
                setStartPreparingComponentText(videoName ?: "")
            }
//                         更新VIP标签的展示 并动态设置评分的 marginStart
            mViewBinding.tvFloatingMembers.isVisible(isShowVip)
            val layoutParams =
                mViewBinding.tvFloatingScoring.layoutParams as (LinearLayout.LayoutParams)
            if (isShowVip) {
                layoutParams.marginStart =
                    resources.getDimensionPixelSize(R.dimen.x20)
            } else {
                layoutParams.marginStart = 0
            }
            mViewBinding.tvFloatingScoring.layoutParams = layoutParams
            //"设置影片名最大宽度
            mViewBinding.tvFloatingName.maxWidth =
                resources.getDimensionPixelSize(R.dimen.x836)
            mViewBinding.tvFloatingName.maxLines = videoMaxLine
            mViewBinding.tvFloatingName.text = videoName
            mViewBinding.tvFloatingScoring.text = score
            mViewBinding.tvFloatingScoring.isVisible(!score.isNullOrEmpty())
            mViewBinding.tvFloatingYear.text = label
            mViewBinding.tvFloatingYear.isGone(label?.isNullOrEmpty() == true)
            mViewBinding.videoDetailHeaderLabel.isGone(mViewBinding.tvFloatingYear.visibility == View.GONE && mViewBinding.tvFloatingScoring.visibility == View.GONE && mViewBinding.tvFloatingMembers.visibility == View.GONE)
            <EMAIL> = hasTrailer
            mViewBinding.detailRecommendList.onLoad(
                episodeGroupInfo = EpisodeGroupInfo(
                    aid = if (Constant.DATA_TYPE_VRS == dataType) aid else id
                        ?: 0,
                    vid = serviceVid,
                    trailerId = trailerId,
                    dateType = dataType,
                    cateCode = cateCode,
                    sortOrder = sortOrder,
                    tvIsEarly = tvIsEarly,
                    albumEpisodeType = albumEpisodeType,
                    tvIsIntrest = tvIsIntrest,
                    videoCount = videoCount,
                    trailerCount = trailerCount,
                    isShowTitle = isShowTitle,
                    episodePlayingVideoOrder = episodePlayingVideoOrder,
                    albumSeries = albumSeries,
                    maxVideoOrder = maxVideoOrder,
                    tvSets = tvSets,
                    updateNotification = updateNotification,
                    hasTrailer = hasTrailer,
                    recommendList = recommendList,
                    isExtendNull = isExtendNull,
                    latestVideoCount = latestVideoCount,
                )
            )
        }
        updateMembersFocusChange(mViewBinding.btnVideoDetailHeaderMembers, false)
        updateHeaderWindow()
        requestPayInfo()
    }


    /**
     * 观察数据返回内容
     */
    private fun observeRender() {


        //片花
        mVideoDetailViewModel.onEach(
            VideoDetailState::trailerEpisodeVideos,
            deliveryMode = UniqueOnly("片花")
        ) {
            AppLogger.d(TAG, "trailerEpisodeVideos Success :${it.toString()}")
            if (it == null || it.videos.isEmpty()) return@onEach
            this.isTrailerTab = true
//            aid = mVideoInfoResponse?.trailerId ?: 0
            vid = it.videos?.get(0)?.tvVerId ?: 0
            mVideoDetailViewModel.requestVideo(
                Constant.DATA_TYPE_VRS, aid = mVideoInfoResponse?.trailerId ?: 0,
                vid = it.videos?.get(0)?.tvVerId ?: 0, false, isChangeVideo = true
            )
        }

        //推荐
        mVideoDetailViewModel.onAsync(
            VideoDetailState::videoDetailRecommendModel, onSuccess = {
                AppLogger.d(TAG, "videoDetailRecommendModel Success :${it.toString()}")
                mViewBinding.detailRecommendList.addRecommend(
                    it.data,
                    false,
                    dataType
                )

            },
            deliveryMode = UniqueOnly("推荐")
        )

        //支付信息
        mVideoDetailViewModel.onEach(
            VideoDetailState::videoPayInfo,
            deliveryMode = UniqueOnly("支付")
        ) {

            AppLogger.d(TAG, "videoPayInfo Success :${it.toString()}")
            mVideoPayInfoResponse = it
            isTicket = mVideoPayInfoResponse?.isTicket ?: false
            isMember = mVideoPayInfoResponse?.isMember ?: false
            isSingle = mVideoPayInfoResponse?.isSingle ?: false
            ticketCount = mVideoPayInfoResponse?.ticketCount ?: 0
            setPayHintMemberPayInfo()
            mViewBinding.tvVideoDetailHeaderPay.text = mVideoPayInfoResponse?.payText ?: ""
            if (mVideoPayInfoResponse?.isShowTime == true) {
                updateTimer(mVideoPayInfoResponse?.time ?: 0)
                mMyCountDownTimer?.cancel()
                mMyCountDownTimer = MyCountDownTimer(mVideoPayInfoResponse?.time ?: 0)
                mMyCountDownTimer!!.start()
                //焦点 用劵后丢失到选集问题
                if (mViewBinding.btnVideoDetailHeaderPay.hasFocus()) {
                    mViewBinding.btnVideoDetailHeaderFull.requestFocus()
                }
                mViewBinding.btnVideoDetailHeaderPay.gone()
            }
            // 教育付费专辑
            if ((it?.feePayVid ?: 0) > 0 && hasLogin) {
                mVideoDetailViewModel.edUserPermission(aid, it?.feePayVid)
            }
            if (resumeUserStatus != 0) {
                AppLogger.d(TAG, "addCommodityData replay")
                if ((resumeUserStatus == 2) && (it?.buttonsNull == true)) {
                    if (TextUtils.equals(
                            mViewBinding.tvVideoDetailHeaderRemainingTime.text,
                            "版权到期，无法购买"
                        )
                    ) {

                    } else {
                        replayPayAfterVideo()
                    }
                } else {
                    AppLogger.d(TAG, "addCommodityData reset needCheckUserStatus = 0")
                }
            }
            updateBottomButtonVisible()
            updateRemainingTime()
        }
        //收藏
        mVideoDetailViewModel.onEach(
            VideoDetailState::enableCollect,
            deliveryMode = UniqueOnly("收藏")
        ) {
            AppLogger.d(TAG, "enableCollect Success :${it.toString()}")
            enableCollect = it
            setCollectionStateImg()
        }


        //教育专辑权限
        mVideoDetailViewModel.onAsync(
            VideoDetailState::edVideoPermissionCheck, onSuccess = {
                AppLogger.d(TAG, "edVideoPermissionCheck Success")

                if (it.data!!.state == -1 || it.data!!.expire_time <= 0) {
                    mVideoDetailViewModel.loadEducationPrivilegeInfo(
                        aid,
                        mVideoPayInfoResponse?.feePayVid
                    )
                }
                mEduVip = true
                val time: Long = it.data!!.expire_time
                val expireDate = Date(time)
                val formatter = SimpleDateFormat("yyyy年MM月dd日")
                val dateText = formatter.format(expireDate)
                mViewBinding.tvVideoDetailHeaderRemainingTime.text =
                    " ${resources.getString(R.string.video_detail_end_time)}：$dateText"
                mViewBinding.tvVideoDetailHeaderRemainingTime.visibility = ConstraintLayout.VISIBLE
                updateBottomButtonVisible()
            }, onFail = {
                mEduVip = false
            },
            deliveryMode = UniqueOnly("教育权限")
        )

        //教育专辑版权
        mVideoDetailViewModel.onAsync(
            VideoDetailState::educationPrivilege, onSuccess = {
                AppLogger.d(TAG, "educationPrivilege Success ")
                mViewBinding.tvVideoDetailHeaderRemainingTime.visibility =
                    ConstraintLayout.VISIBLE
                mViewBinding.tvVideoDetailHeaderRemainingTime.text = "版权到期，无法购买"
                mViewBinding.btnVideoDetailHeaderPay.isClickable = false
                updateBottomButtonVisible()
            },
            deliveryMode = UniqueOnly("教育专辑")
        )

        mVideoDetailViewModel.onAsync(VideoDetailState::pgcAlbumInfo, onSuccess = { pgc ->
            dataType = Constant.DATA_TYPE_PGC
            when (val videoInfoResponse = getViewResult(false, pgc.convertToAlbumInfo())) {
                is VideoDetailViewResult.Loading -> {
                    if (isDlna) {
                        startVideoWindowFull()
                    }
                }

                is VideoDetailViewResult.Success -> {
                    mVideoInfoResponse = videoInfoResponse.response
                    albumSuccess()
                }

                is VideoDetailViewResult.Fail -> {
                    albumError(videoInfoResponse.errorMessage)
                }
            }
        }, onFail = {
            albumError((getViewResult(false, null) as VideoDetailViewResult.Fail).errorMessage)
        })

        mVideoDetailViewModel.onAsync(VideoDetailState::vrsAlbumInfo, onSuccess = { vrs ->
            dataType = Constant.DATA_TYPE_VRS
            when (val videoInfoResponse = getViewResult(true, vrs)) {
                is VideoDetailViewResult.Loading -> {
                    if (isDlna) {
                        startVideoWindowFull()
                    }
                }

                is VideoDetailViewResult.Success -> {
                    mVideoInfoResponse = videoInfoResponse.response
                    albumSuccess()
                }

                is VideoDetailViewResult.Fail -> {
                    albumError(videoInfoResponse.errorMessage)
                }
            }
        }, onFail = {
            albumError((getViewResult(false, null) as VideoDetailViewResult.Fail).errorMessage)
        })


        /**
         * 下一集信息
         */
        mVideoDetailViewModel.onEach(
            VideoDetailState::nextEpisodeInfo,
            deliveryMode = UniqueOnly("下一集")
        ) {
            if (it == null) return@onEach
            nextEpisodeInfo = it
            mVideoPlayMenuConfigComponent?.nextEpisodeInfo = it
            val hasNext = nextEpisodeInfo.size > 4
            val name = if (hasNext) nextEpisodeInfo[4].toString() else ""
            mVideoNextPlayPromptComponent?.setNextInfo(hasNext, name)
            AppLogger.d(TAG, "nextEpisodeInfo Success :${nextEpisodeInfo.toString()}")
        }
    }

    private fun getServiceTvPlayType(): Int {
        return mVideoPlayInfoResponse?.tvPlayType ?: 0
    }

    /**
     * 是否是切换清晰度播放
     * 用于 drm 切换清晰度相关判断
     */
    private var isChangeResolution = false
    private fun observeVideoRender() {
        /**
         * drm 鉴权播放信息
         */
        mVideoDetailViewModel.onAsync(VideoDetailState::drmPlayInfoData, onSuccess = {
            val drmBean = SofaDrmBean()
            var url = mCurrentUrl
            //拼接原有地址参数 cdn和播放器需要
            val pathParams = mCurrentUrl?.split("?")?.getOrNull(1) ?: ""
            drmBean.playUrlMpd = it.data?.playUrlMpd.plus("&" + pathParams) ?: ""
            AppLogger.d(TAG, "drmPlayInfoData  url:${it.toString()}")
            AppLogger.d(TAG, "drmPlayInfoData  pathParams url:${pathParams.toString()}")
            when (it.code) {
                200 -> {
                    url = drmBean.playUrlMpd
                }

                1000 -> {
                    url = drmBean.playUrlMpd
                    tvPlayType = 3
                    mBaseOptionModel.drmType = SofaPlayerConfig.SOFA_DRM_MARLIN
                }

                1001 -> {
                    setPayHintText(HINT_TEXT_DRM_ERROR)
                    return@onAsync
                }

                else -> {
                    setPayHintText(HINT_TEXT_DRM_ERROR)
                    return@onAsync
                }
            }
            //外采drm sofa播放器需要的设置

            when (mBaseOptionModel.drmType) {
                SofaPlayerConfig.SOFA_DRM_WIDEVINE -> {
                    drmBean.drmLicenseUrl = it.data?.license
                    drmBean.pssh = it.data?.pssh
                    mViewBinding.detailShVideo.changeFilmRenderFactory(SurfaceViewRenderFactory())
                    changeFilmPlayerFactorySofa()
                }

                SofaPlayerConfig.SOFA_DRM_SOHU -> {

                }

                SofaPlayerConfig.SOFA_DRM_MARLIN -> {
                    drmBean.drmContentId = it.data?.kid
                    drmBean.drmLicenseUrl = it.data?.license
                    changeFilmPlayerFactorySofa()
                }
            }
            /**
             * test数据开始**
             */
//            drmBean.drmContentId = "urn:marlin:kid:ab000000000000000000000925566367"
//            drmBean.drmLicenseUrl =
//                "https://uat-hot.vrs.sohu.com/v2/video/drm/license.action?type=3&tvId=604434254"
//            drmBean.playUrlMpd =
//                "https://uat-hot.vrs.sohu.com/v2/video/drm/playMpd.mpd?vid=9374514&type=3&templateType=1&plat=15&pt=6&prod=ott&pg=1&ch=v&qd=9999&cv=7.4.0&uid=02ffff10740010945d572853b3788c6f8140b4fd11bf85&player=1.0&p_type=1&ssl=1"
//            url =
//                "https://uat-hot.vrs.sohu.com/v2/video/drm/playMpd.mpd?vid=9374514&type=3&templateType=1&plat=15&pt=6&prod=ott&pg=1&ch=v&qd=9999&cv=7.4.0&uid=02ffff10740010945d572853b3788c6f8140b4fd11bf85&player=1.0&p_type=1&ssl=1"
            /**
             * test数据结束**
             */

            mBaseOptionModel.drmBean = drmBean
            if (isChangeResolution) {
                isChangeResolution = false
                changeResolutionAction(currentResolutionInfo!!, url)
            } else {
                mShDataSource.url = url
                setVideoOption()
                prepareVideo()
            }

        }, onFail = {
            setPayHintText(HINT_TEXT_DRM_ERROR)
            AppLogger.d(TAG, "drmPlayInfoData onFail:${it.localizedMessage}")
        })

        /**
         * pgc影片信息
         */
        mVideoDetailViewModel.onAsync(
            VideoDetailState::pgcVideoInfoResult, onSuccess = { pgcInfo ->
                AppLogger.d(TAG, "pgcVideoInfo Success :${pgcInfo.toString()}")
                mBaseOptionModel = SofaOptionModel()
                onlySeeIndex = 0
                mCurrentOnlySeeMenuItem = null
                setCurrentOnlySeeMenuItem()
                initVideoPermissionParams()
                setCopyrightTips()
                mVideoPlayInfoResponse = VideoPlayInfoResponse()
                mVideoPlayInfoResponse?.resolutionInfos = pgcInfo
                mTvEndTime = 0
                mTvStartTime = 0
                isTryVideo = false
                southMediaCheck(0)
                getPlayHistoryByIdTime()
                updatePgcRecommendAndEpiside()
            }, onFail = {
                setPayHintText(HINT_TEXT_ERROR)
                updatePgcRecommendAndEpiside()
            },
            deliveryMode = UniqueOnly("pgc")
        )
        //Vrs影片信息
        mVideoDetailViewModel.onAsync(
            VideoDetailState::detailVideoResult,
            onSuccess = { result ->
                AppLogger.d(TAG, "detailVideoResult Success :${result.toString()}")
                mBaseOptionModel = SofaOptionModel()
                mVideoPlayInfoResponse = result ?: VideoPlayInfoResponse()
                tvPlayType = getServiceTvPlayType()
                vid = mVideoPlayInfoResponse?.videoVid?.toInt() ?: 0
                feeType = mVideoPlayInfoResponse?.feeType ?: FEE_FREE
                initVideoPermissionParams()
                setCopyrightTips()
                tvCateCode = mVideoPlayInfoResponse?.cateCode ?: 0
                logoInfo = mVideoPlayInfoResponse?.logoInfo
                //添加只看他模块
                mVideoPlayMenuConfigComponent?.also {
                    it.videoOrientation = logoInfo?.orientation ?: 0
                    it.videoType = mVideoPlayInfoResponse?.videoType ?: Constant.DATA_TYPE_VRS
                }
                //设置影片展示名称
                mVideoPlayControlComponent?.setTitle(mVideoPlayInfoResponse?.videoTvName)
                setStartPreparingComponentText(mVideoPlayInfoResponse?.videoTvName ?: "")
                mVideoPlayControlComponent?.videoLength =
                    mVideoPlayInfoResponse?.videoLength ?: 0
                mVideoNormalProgressComponent?.videoLength =
                    mVideoPlayInfoResponse?.videoLength ?: 0
                mVideoPlayMenuConfigComponent?.videoLength =
                    mVideoPlayInfoResponse?.videoLength ?: 0
                //设置logo 遮标 初始化信息
                mTvEndTime = mVideoPlayInfoResponse?.tvEndTime?.toLong()?.let {
                    AdTsManger.getInstants().getCalculateSkipTailTime(
                        it
                    )
                } ?: 0L
                mTvEndTime = AdTsManger.getInstants().getCalculateSkipTailTime(
                    (mVideoPlayInfoResponse?.tvEndTime?.toLong() ?: 0L)
                )
                mTvStartTime = mVideoPlayInfoResponse?.tvStartTime?.toLong() ?: 0L
                setPayHintFeeType()
                refreshIsTryVideo()
                val feeType = mVideoPlayInfoResponse?.feeType
                if (isTryVideo && feeType != FEE_ALBUM) {
                    southMediaCheck(0)
                    getPlayHistoryByIdTime()
                } else if (feeType == FEE_ALBUM) {
                    if (isDlna) {
                        requestUserVideoPermission(aid, vid)
                    } else if (hasLogin) {
                        southMediaCheck(1)
                        requestUserVideoPermission(aid, vid)
                    } else {
                        southMediaCheck(1)
                        hasPermissionCheckSuccess = false
                        getPlayHistoryByIdTime()
                    }
                } else if (feeType == FEE_VIDEO) {
                    if (isDlna) {
                        requestUserVideoPermission(aid, vid)
                    } else if (hasLogin) {
                        southMediaCheck(1)
                        var realAid = 0
                        if (mVideoPlayInfoResponse?.tvStype == 38) { //番外，用片花专辑请求鉴权
                            realAid = mVideoPlayInfoResponse?.playlistId ?: 0
                        } else {
                            realAid = aid
                        }
                        requestUserVideoPermission(realAid, vid)
                    } else {
                        if (mVideoPlayInfoResponse?.cateId == 21) {
                            if (isFull) {
                                startVideoWindowNormal()
                            }
                            setPayHintText(HINT_TEXT_NORMAL)
                            setResumeUserStatus(2)
                            startPayActivity = false
                            ActivityLauncher.startPayActivity(
                                this,
                                aid,
                                mVideoPayInfoResponse?.feePayVid ?: 0,
                                mVideoPlayInfoResponse?.videoTvName,
                                mVideoPlayInfoResponse?.tvVerPic,
                                1,
                                PayActivity.PAY_SOURCE_DETAIL,
                                21
                            )
                        } else {
                            if (isFull) {
                                startVideoWindowNormal()
                            }
                            setPayHintText(HINT_TEXT_NORMAL)
                            setResumeUserStatus(2)
                            startPayActivity = false
                            ActivityLauncher.startPayActivity(
                                this,
                                ParamConstant.PAGE_SOURCE_PLAYER,
                                mPaySourceComeFromType,
                                false
                            )
                        }
                        startPayActivity = true
                    }
                } else {
                    southMediaCheck(0)
                    getPlayHistoryByIdTime()
                }
                updateVrsRecommendAndEpiside()

            },
            onFail = {
                setPayHintText(HINT_TEXT_ERROR)
                updateVrsRecommendAndEpiside()
                AppLogger.d(TAG, "detailVideoResult onFail :${it.toString()}")
            },
            deliveryMode = UniqueOnly("Vrs")
        )
    }

    private fun updateVrsRecommendAndEpiside() {
        if (mVideoPlayInfoResponse == null) mVideoPlayInfoResponse = VideoPlayInfoResponse()
        //初始化播控选集数据
        if (mVideoPlayInfoResponse?.isisTrailer == true) {
            if (mVideoPlayInfoResponse?.playlistId != aid) {
                val layoutType = chooseLayoutType(
                    true,
                    tvCateCode,
                    mVideoPlayInfoResponse!!.isShowTitle,
                    mVideoPlayInfoResponse!!.videoType,
                    0
                )
                mVideoPlayMenuConfigComponent?.episodeLayoutUpdate(
                    mVideoPlayInfoResponse!!.playlistId,
                    mVideoPlayInfoResponse!!.videoVid.toInt(),
                    mVideoPlayInfoResponse!!.videoType,
                    tvCateCode,
                    1,
                    layoutType,
                    true,
                    mVideoPlayInfoResponse!!.videoCount,
                    mVideoPlayInfoResponse!!.videoOrder,
                    mVideoPlayInfoResponse!!.recommendList?.toList() ?: arrayListOf()
                )
            } else {
                val layoutType = chooseLayoutType(
                    true,
                    tvCateCode,
                    mVideoPlayInfoResponse!!.isShowTitle,
                    mVideoPlayInfoResponse!!.videoType,
                    mVideoPlayInfoResponse!!.tvStype
                )
                mVideoPlayMenuConfigComponent?.episodeLayoutUpdate(
                    aid,
                    mVideoPlayInfoResponse!!.videoVid.toInt(),
                    mVideoPlayInfoResponse!!.videoType,
                    tvCateCode,
                    mVideoPlayInfoResponse!!.sortOrder,
                    layoutType,
                    false,
                    mVideoPlayInfoResponse!!.videoCount,
                    mVideoPlayInfoResponse!!.videoOrder,
                    mVideoPlayInfoResponse!!.recommendList?.toList() ?: arrayListOf()
                )

            }
        } else {
            val layoutType = chooseLayoutType(
                false,
                tvCateCode,
                mVideoPlayInfoResponse!!.isShowTitle,
                mVideoPlayInfoResponse!!.videoType,
                0
            )
            mVideoPlayMenuConfigComponent?.episodeLayoutUpdate(
                getCurrentAid()!!,
                mVideoPlayInfoResponse!!.videoVid.toInt(),
                mVideoPlayInfoResponse!!.videoType,
                tvCateCode,
                mVideoPlayInfoResponse!!.sortOrder,
                layoutType,
                false,
                mVideoPlayInfoResponse!!.videoCount,
                mVideoPlayInfoResponse!!.videoOrder,
                mVideoPlayInfoResponse!!.recommendList?.toList() ?: arrayListOf()
            )
        }
        mViewBinding.detailRecommendList.updateAfterPlayer(
            hasEpisode,
            mVideoPlayInfoResponse!!.videoOrder,
            if (mVideoInfoResponse?.cateCode == Constant.CATECODE_MOVIE || mVideoPlayInfoResponse?.tvStype == 38) false else (aid != mVideoPlayInfoResponse?.playlistId),
            mVideoPlayInfoResponse!!.videoVid.toInt()
        )//电影没有花絮tab，默认使用正片的aid
        requestDetailRecommendAll()
        getNextEpisodeInfo()
    }

    private fun updatePgcRecommendAndEpiside() {
        if (mVideoPlayInfoResponse == null) mVideoPlayInfoResponse = VideoPlayInfoResponse()

        val videoCount = if (TextUtils.isEmpty(mVideoInfoResponse?.latestVideoCount)) {
            0
        } else {
            mVideoInfoResponse?.latestVideoCount?.toInt()
        } ?: 0
        mVideoPlayMenuConfigComponent?.episodeLayoutUpdate(
            mVideoInfoResponse?.id ?: 0,
            aid,
            dataType,
            tvCateCode,
            0,
            chooseLayoutType(
                isTrailer = false,
                tvCateCode,
                mVideoInfoResponse?.isShowTitle ?: 0,
                dataType,
                0
            ),
            false,
            videoCount,
            mVideoInfoResponse?.videoOrder ?: 0,
            mVideoInfoResponse?.recommendList
        )
        mViewBinding.detailRecommendList.updateAfterPlayer(
            hasEpisode,
            mVideoInfoResponse?.videoOrder ?: 0,
            false,
            mVideoInfoResponse?.serviceVid ?: 0
        )
        requestDetailRecommendAll()
        getNextEpisodeInfo()
    }

    /**
     * 视频鉴权
     */
    private fun requestUserVideoPermission(aid: Int, vid: Int) {
        if (isDlna && isShDlnaVideo) {
            mVideoDetailViewModel.loadIphoneMKey(
                mDlnaMode?.passport,
                mDlnaMode?.authToken,
                aid,
                vid,
                mDlnaMode?.apikey,
                mDlnaMode?.uid,
                mDlnaMode?.appVersion,
                mDlnaMode?.gid,
                mDlnaMode?.app_id,
                mDlnaMode?.plat,
                mDlnaMode?.appId,
                mDlnaMode?.ua
            )
        } else {
            mVideoDetailViewModel.requestUserVideoPermission(aid, vid)
        }

    }

    /**
     * 设置试看提示文案信息
     */
    private fun setCopyrightTips() {
        mVideoTrySeeComponent?.copyrightTips = mVideoInfoResponse?.copyRightTips ?: ""
    }

    /**
     * 设置即将播放提示文案
     */
    private fun setStartPreparingComponentText(text: String?) {

        mVideoStartPreparingComponent?.setTipText(text ?: "")
//
    }

    /**
     * 拼接 剧享片头的地址
     */
    private fun appendAdTsUrl() {
        mCurrentUrl = mCurrentUrl?.appendAdTsUrl()
        AppLogger.d("appendAdTsUrl:$mCurrentUrl")
    }

    /**
     * 获取播放历史记录时间
     */
    private fun getPlayHistoryByIdTime() {
        refreshIsTryVideo()
        playHistoryService.getPlayHistoryById(
            dataType,
            if (dataType == Constant.DATA_TYPE_VRS) aid else mVideoInfoResponse?.serviceVid ?: aid,
            object : PlayHistoryListener {
                override fun onSuccess(playHistoryList: List<PlayHistory>?) {
                    var logMsg = ""
                    if (thirdPlayTime != -1) {
                        currentHistoryWatchTime = thirdPlayTime.toLong() / 1000
                        logMsg = "thirdPlayTime is $thirdPlayTime"
                    } else if (playHistoryList?.isNotEmpty() == true) {
                        val oldVid: Int =
                            if (dataType == Constant.DATA_TYPE_VRS) mVideoPlayInfoResponse?.videoVid
                                ?.toInt() ?: 0 else aid
                        val newVid: Int =
                            if (dataType == Constant.DATA_TYPE_VRS) playHistoryList[0].videoId else playHistoryList[0].videoId
                        if (isTryVideo) {
                            logMsg = "try video"
                            currentHistoryWatchTime = 0L
                        } else if (newVid != oldVid) {
                            logMsg = "newVid:$newVid oldVid:$oldVid"

                            currentHistoryWatchTime = 0L
                        } else if (playHistoryList[0].isTryVideo) {
                            currentHistoryWatchTime = playHistoryList[0].watchTime.toLong()
                            logMsg = "playHistoryList is isTryVideo"
                        } else if (isDlna) {
                            logMsg = "is dlna"
                            currentHistoryWatchTime = 0
                        } else {
                            currentHistoryWatchTime = playHistoryList[0].watchTime.toLong()
                            if (currentHistoryWatchTime > 0) {
                                currentHistoryWatchTime =
                                    AdTsManger.getInstants().getCalculateHistoryTime(
                                        currentHistoryWatchTime * 1000,
                                        true
                                    ) / 1000
                                logMsg = "currentHistoryWatchTime >0 "
                            }

                        }
                    } else {
                        currentHistoryWatchTime = 0L
                        logMsg = "历史记录为空"
                    }
                    setPlayInfo()
                    AppLogger.d(TAG, "获取历史记录时间 :${logMsg.plus(currentHistoryWatchTime)}")
                }

                override fun onFail(reason: String, playHistoryList: List<PlayHistory>) {
                    currentHistoryWatchTime = 0L
                    setPlayInfo()
                    AppLogger.d(TAG, "获取历史记录时间 fail reason：$reason  ")
                }
            })

    }

    fun seekTo(time: Long) {
        mViewBinding.detailShVideo.seekTo(time)
    }


    /**
     * 立即跳过片头
     */
    private fun skipStartTimeNow() {
        if (!PlayerConfig.enableSkipStartAndEndPosition && isTryVideo) return
        if (mPosition < mTvStartTime && mTvStartTime > 0 && mPosition > 0) {
            seekTo(mTvStartTime * 1000)
            skipStart()
        } else if (mPosition > 0 && mTvEndTime in 1..<mPosition) {
            skipEnd()
            mViewBinding.detailShVideo.getFilmVideoController().also {
                it?.playerControl?.completion()
            }
        }
    }

    /**
     * 此判断是由于搜狐视频视频资源在 ott 找不到 投屏播放原始资源
     */
    private fun dlnaOttPlayUlrCheck(): Boolean {
        if (mCurrentUrl.isNullOrEmpty() && isDlna && isShDlnaVideo) {
            showVideoView()
            dlnaNormalPlay()
            return true
        }
        return false
    }

    //设置播放信息数据
    private fun setPlayInfo() {
        conversionResolution()
        AppLogger.d(TAG, "setPlayInfo fun conversionResolution info is :$mCurrentUrl")
        //此判断是由于搜狐视频视频资源在 ott 找不到播放原始资源
        if (dlnaOttPlayUlrCheck()) {
            return
        }
        PlayerConfig.enableSkipStartAndEndPosition =
            if (isDlna || isTryVideo) false else PlaySettingHelper.getNeedSkipHeaderAndEnd()

        conversionOnlySeeData()
        if (mCurrentUrl?.isNullOrEmpty() == false) {
            if (getHttpsParams(this) == 1) {
                mCurrentUrl = mCurrentUrl?.replace("http", "https").plus("&ssl=1")
            }
            if (mCurrentUrl?.contains("httpss://") == true) {
                mCurrentUrl = mCurrentUrl?.replace("httpss", "https");
            }
        }
        if (dataType == Constant.DATA_TYPE_VRS) {
            mShDataSource.videoVid = mVideoPlayInfoResponse?.videoVid ?: 0
            mShDataSource.videoLength = mVideoPlayInfoResponse?.videoLength
        } else {
            mShDataSource.videoVid = aid.toLong()
            mShDataSource.videoLength = mVideoInfoResponse?.videoLength?.toLong()
        }
        mShDataSource.enableFLogo = true
        mShDataSource.passport = passport
        //dlna 搜狐视频来源的起播时间
        if (isDlna) {
            try {
                currentHistoryWatchTime = (mDlnaMode?.startPos?.toLong() ?: 0L)
                AppLogger.d(TAG, "dlna startPos is $currentHistoryWatchTime")
            } catch (e: Throwable) {
                AppLogger.e(TAG, "dlna catch is " + e.localizedMessage)
                AppLogger.e(
                    TAG,
                    "dlna catch currentHistoryWatchTime error to long ,startPos is :${mDlnaMode?.startPos}"
                )
            }
        }
        mBaseOptionModel.also {
            it.enableAccurateSeek = false
            if (!isTryVideo) {
                it.endPosition = mTvEndTime * 1000
                it.skipStartPosition = mTvStartTime * 1000
                val start =
                    currentHistoryWatchTime
                it.startPosition = start * 1000
            } else {
                val start =
                    currentHistoryWatchTime
                it.startPosition = start * 1000
                it.endPosition = 0
            }
        }
        var isSkip = false
        if ((mVideoPlayInfoResponse?.feeType ?: 0) > 0) {
            isSkip = true
        }
        if ((hasVip && hasLogin) || TeenagersManger.isTeenager()) {
            isSkip = true
        }
        if (Util.isFirstSkipAd(this)) {
            isSkip = true
            Util.setFirstSkipAd(this, false)
        }
        if (isDlna) {
            isSkip = true
        }
        /**
         * 针对长虹5508做处理，去掉广告
         */
        val model = if (Build.MODEL == null) "" else Build.MODEL
        if ("KTU84M" == Build.ID ||
            (model.contains("ChangHong Android TV") && "LMY47V" == Build.ID)
        ) {
            isSkip = true
        }
        if (!startPayActivity || isStartLoginResolutionApp) {
            isSkip = true
        }
        isStartLoginResolutionApp = false
        //设置播放数据
        mShDataSource.adSkip = isSkip
        mShDataSource.also {
            it.gid = UserInfoHelper.getGid() ?: ""
            it.videoAid = aid.toLong()
            it.videoAreaId = tvAreaId.toLong()
            it.videoCateCode = tvCateCode.toLong()
            it.videoType = dataType
        }
        SohuDlnaManger.getInstance().mCurrentPlayVid = vid

        when (tvPlayType) {
            3 -> {
                /**
                 *  先校验服务器配置是否需要强制使用MARLIN
                 *  之后再判断设备是都支持WidevineL
                 *  其他情况使用MARLIN兜底
                 */
                try {
                    if (UserConfigHelper.getUserDrmPlayParams() == 1) {
                        AppLogger.d(TAG, "drm 设备支持 MARLIN 来源服务器")
                        tvPlayType = 3
                        mBaseOptionModel.drmType = SofaPlayerConfig.SOFA_DRM_MARLIN
                        requestDrmPlayInfo()
                        return
//                    } else if (SofaMediaPlayer.isSupportWidevineL1(this.applicationContext)) {
                    } else if (isSupportWidevineL1()) {
                        tvPlayType = 3
                        mBaseOptionModel.drmType = SofaPlayerConfig.SOFA_DRM_WIDEVINE
                        requestDrmPlayInfo()
                        AppLogger.d(TAG, "drm 设备支持 Widevine 来源播放器")
                        return
                    } else {
                        tvPlayType = 3
                        AppLogger.d(TAG, "drm 设备支持 MARLIN 来源播放器")
                        mBaseOptionModel.drmType = SofaPlayerConfig.SOFA_DRM_MARLIN
                        requestDrmPlayInfo()
                        return
                    }
                } catch (e: Throwable) {
                    tvPlayType = 1
                    mBaseOptionModel.drmType = SofaPlayerConfig.SOFA_DRM_SOHU
                    AppLogger.e(TAG, "drm处异常切换至sohu：${e.localizedMessage}")
                }
//                mViewBinding.detailShVideo.setFilmPlayerFactory(SofaPlayerFactory.create())
            }

            1 -> {
                mBaseOptionModel.drmType = SofaPlayerConfig.SOFA_DRM_SOHU
                AppLogger.d(TAG, "drm sohu")
            }

            else -> {
                mBaseOptionModel.drmType = SofaPlayerConfig.SOFA_DRM_NONE
                AppLogger.d(TAG, "drm none")
                //请求广告获取广告ts 拼接m3u8 地址 重新设置播放地址  非投屏 非系统播放器
                setRequestAdTs(
                    dataType = dataType,
                    vid = mVideoPlayInfoResponse?.videoVid,
                    aid = aid,
                    tvInsertAdTime = mVideoPlayInfoResponse?.tvInsertAdTime,
                    serviceVersionId = currentResolutionInfo?.videoPlayInfo?.serviceVersionId ?: 1,
                    isDlna
                )
                appendAdTsUrl()
            }

        }
        AppLogger.d(TAG, "设备使用drmType: ${mBaseOptionModel.drmType}")
        setVideoOption()
        if (hasPermissionCheckSuccess && mCurrentUrl?.isNotEmpty() == true && !mVideoPermissionKey.isNullOrEmpty()) {
            mCurrentUrl = mCurrentUrl?.plus("&mkey=$mVideoPermissionKey")
            AppLogger.d("appendmkeyUrl:$mCurrentUrl")
        }
        mShDataSource.url = mCurrentUrl
        //异步起播
        prepareVideo()
    }
    private fun isSupportWidevineL1(): Boolean {
       return SofaMediaPlayer.isSupportWidevineL1(this.applicationContext)
//       return false
    }

    private fun setVideoOption() {
        mViewBinding.detailShVideo.setOption(mBaseOptionModel)
    }

    private fun changeFilmPlayerFactorySofa() {
        if (ShPlayerConfig.filmPlayerFactory is SystemPlayerFactory) {
            AppLogger.d("changeFilmPlayerFactorySofa")
            mViewBinding.detailShVideo.setFilmPlayerFactory(SofaPlayerFactory.create())
        }
    }


    /**
     * 获取历史记录Vid
     *  dlna 不读历史记录
     */
    private fun getHistoryVid() {
        playHistoryService.getPlayHistoryById(
            dataType,
            if (dataType == Constant.DATA_TYPE_VRS) aid else mVideoInfoResponse?.serviceVid ?: aid,
            object : PlayHistoryListener {
                override fun onSuccess(playHistoryList: List<PlayHistory>?) {
                    if (!isDlna) {
                        if (playHistoryList?.isNotEmpty() == true) {
                            vid =
                                if (dataType == Constant.DATA_TYPE_VRS) playHistoryList[0].videoId else playHistoryList[0].videoId

                        } else {
                            vid =
                                if (dataType == Constant.DATA_TYPE_VRS) mVideoInfoResponse?.serviceVid
                                    ?: 0 else mVideoInfoResponse?.serviceVid ?: aid

                        }
                    } else {
                        if (vid == 0 || vid == -1) {
                            vid =
                                if (dataType == Constant.DATA_TYPE_VRS) mVideoInfoResponse?.serviceVid
                                    ?: 0 else mVideoInfoResponse?.serviceVid ?: aid
                        }
                    }
                    mVideoDetailViewModel.requestVideo(
                        dataType,
                        aid = aid,
                        vid = vid,
                        false,
                        isChangeVideo = false
                    )
                }

                override fun onFail(reason: String, playHistoryList: List<PlayHistory>) {
                    if (isDlna) {
                        if (vid == 0 || vid == -1) {
                            vid =
                                if (dataType == Constant.DATA_TYPE_VRS) mVideoInfoResponse?.serviceVid
                                    ?: 0 else mVideoInfoResponse?.serviceVid ?: aid
                        }
                    } else {
                        vid =
                            if (dataType == Constant.DATA_TYPE_VRS) mVideoInfoResponse?.serviceVid
                                ?: 0 else mVideoInfoResponse?.serviceVid ?: aid
                    }
                    mVideoDetailViewModel.requestVideo(
                        dataType,
                        aid = aid,
                        vid = vid,
                        false,
                        isChangeVideo = false
                    )
                }
            })
    }


    /**
     * 保存历史记录
     */
    private fun saveHistory() {
        if (isDlna) {
            return
        }
        if (mDuration == 0L) {
            return
        }
        //搜狐学堂不保存历史记录
        if (tvCateCode == CornerTagImageView.CORNER_TYPE_SOHUCLASS) {
            return
        }
        if (isTrailerTab || mVideoPlayInfoResponse?.tvStype == 38) {
            return
        }
        val playHistory = PlayHistory().also {
            it.videoOrder = mVideoPlayInfoResponse?.videoOrder
            if (PlaySettingHelper.getNeedSkipHeaderAndEnd() && mTvEndTime > 0) {
                if ((mPosition > mTvEndTime - 29) && !isTryVideo) {
                    it.watchTime = 0
                } else {
                    it.watchTime =
                        (AdTsManger.getInstants()
                            .getCalculateHistoryTime(mPosition * 1000.toLong(), false) / 1000)
                            .toInt()
                }
            } else {
                if ((mPosition > mDuration - 29 && !isTryVideo)) {
                    it.watchTime = 0
                } else {
                    it.watchTime =
                        (AdTsManger.getInstants()
                            .getCalculateHistoryTime(mPosition * 1000.toLong(), false) / 1000)
                            .toInt()
                }
            }

            if (dataType == Constant.DATA_TYPE_VRS) {
                it.dataType = Constant.DATA_TYPE_VRS
                it.albumId =
                    if (mVideoPlayInfoResponse?.tvStype == 1) aid else mVideoPlayInfoResponse?.playlistId
                it.videoVerPic = mVideoPlayInfoResponse?.tvVerPic
                it.fee = mVideoInfoResponse?.tvIsFee
                it.ottFee = mVideoPlayInfoResponse?.tvOttIsFee
                it.categoryCode = mVideoPlayInfoResponse?.cateCode
                it.videoOrder = mVideoPlayInfoResponse?.videoOrder
                it.tvName =
                    if (mVideoPlayInfoResponse?.tvStype == 1) mVideoPlayInfoResponse?.albumTVName else mVideoPlayInfoResponse?.trailerTVName
                it.episode = mVideoPlayInfoResponse?.videoTvName
                it.tvSets = mVideoPlayInfoResponse?.videoCount
                it.tvSetNow = mVideoPlayInfoResponse?.latestVideoCount
                it.videoHorPic = mVideoPlayInfoResponse?.videoExtendsPic_640
                it.tvIsEarly = mVideoInfoResponse?.tvIsEarly
                it.useTicket = mVideoInfoResponse?.useTicket
                it.paySeparate = mVideoInfoResponse?.paySeparate
                it.cornerType = mVideoInfoResponse?.cornerType
                it.isTryVideo = isTryVideo
                it.videoId = vid
            } else {
                it.dataType = Constant.DATA_TYPE_PGC
                it.albumId = aid
                it.fee = mVideoInfoResponse?.tvIsFee
                it.ottFee = mVideoInfoResponse?.ottFee
                it.categoryCode = mVideoInfoResponse?.cateCode
                it.episode = mVideoInfoResponse?.videoName
                it.tvName = mVideoInfoResponse?.videoName
                it.isAudit = mVideoInfoResponse?.isAudit
                it.tvSets =
                    if (TextUtils.isEmpty(mVideoInfoResponse?.tvSets)) 0 else mVideoInfoResponse?.tvIsFee!!.toInt()
                it.tvSetNow =
                    if (TextUtils.isEmpty(mVideoInfoResponse?.latestVideoCount)) 0 else mVideoInfoResponse?.latestVideoCount!!.toInt()
                it.videoHorPic = Util.generateHorPicUrl(
                    mVideoInfoResponse?.albumExtendsPic_640_360,
                    640,
                    360,
                    80
                )
                it.cornerType = mVideoInfoResponse?.cornerType
                it.tvIsEarly = mVideoInfoResponse?.tvIsEarly
                it.useTicket = mVideoInfoResponse?.useTicket
                it.paySeparate = mVideoInfoResponse?.paySeparate
                it.isTryVideo = false
                it.secondCategoryCode = mVideoInfoResponse?.cateCodeSecond
                it.videoId = mVideoInfoResponse?.serviceVid
            }
            it.tvLength = (if (mDuration == 0L) mVideoPlayInfoResponse?.videoLength
                ?: 0 else mDuration).toInt()


        }

        //For all type videos
        if (mPosition < 1) {
            //小于1秒不记录播放记录,但有可能给第三方发广播
            sendHistoryBroadcast(playHistory)
            return
        }
        playHistoryService.addPlayHistory(
            playHistory,
            Constant.EDU_CATE_CODE == mVideoPlayInfoResponse?.cateCode
        )
        if (this.packageName == "com.sohuott.tv.vod.xiaomi") {
            sendBroadCastToXiaoMi()
        }
        sendHistoryBroadcast(playHistory)
    }

    private fun sendHistoryBroadcast(playHistory: PlayHistory) {
        if (ThirdPartyService.getService(this) != null && mVideoPlayInfoResponse?.isisTrailer == false) {
            if (mVideoPlayInfoResponse?.isExtendNull == false && (mVideoPlayInfoResponse?.maxVideoOrder
                    ?: 0) > 1
            ) {
                playHistory.reserveredInt1 = mVideoPlayInfoResponse?.videoOrder
            } else {
                playHistory.reserveredInt1 = -1
            }

            playHistory.reserveredInt2 = mVideoInfoResponse?.tvIsFee
            playHistory.reserveredInt3 = mVideoPlayInfoResponse?.maxVideoOrder
            playHistory.reserveredString1 = mVideoPlayInfoResponse?.varietyPeriod

            ThirdPartyService.getService(this).historyAdd(playHistory)
            ThirdPartyService.release()
        }
    }

    private fun sendBroadCastToXiaoMi() {
        //发送广播
        val intent = Intent()
        intent.setAction("com.sohuott.tv.vod.xiaomi.action.HISTORY")
        intent.putExtra("tv_length", mDuration)
        intent.putExtra("watch_time", mPosition)
        if (dataType == Constant.DATA_TYPE_VRS) {
            intent.putExtra("media_id", aid)
        } else {
            intent.putExtra("media_id", vid)
        }
        if (mViewBinding.detailRecommendList.getEpisodeGroupLayout()
                ?.getEpisodeSortOrder() == EpisodeLayoutNew.DESC_SORT_ORDER
        ) {
            intent.putExtra(
                "video_order", (mVideoPlayInfoResponse?.videoCount
                    ?: 0) - (mVideoPlayInfoResponse?.videoOrder ?: 0)
            )
        } else {
            intent.putExtra("video_order", mVideoPlayInfoResponse?.videoOrder)
        }
        intent.putExtra("video_count", mVideoPlayInfoResponse?.videoCount ?: 0)
        this.sendBroadcast(intent)
    }


    /**
     * 请求收藏数据
     */
    private fun requestCollect() {
        if (hasLogin) {
            mVideoDetailViewModel.loadChaseStatus(tvCateCode, aid)
        } else {
            enableCollect = mCollectionHelper.requestRecordById(
                aid
            )
            setCollectionStateImg()
        }
    }

    /**
     * 校验是否是需要花屏配置
     */
    private fun huaPingCheck() = (Util.getHuapingParams(this) == 1)


    /**
     * 配置花屏时 小窗./全屏和状态 判断是否播放处理
     */
    private fun huaPingStartOrPause() {
        AppLogger.d(TAG, "花屏配置 screenMode：$screenMode  videoPlayState:$videoPlayState")
        if (screenMode == PlayerConstants.ScreenMode.FULL) {
            when (videoPlayState) {
                PlayerConstants.VideoState.PAUSED -> {
                    mVideoHuaPingComponent?.hide()
                    mViewBinding.detailShVideo.resume()
                }

                else -> {
                    prepareVideo()
                }
            }
        } else {
            when (videoPlayState) {
                PlayerConstants.VideoState.PLAYING,
                PlayerConstants.VideoState.BUFFERED,
                PlayerConstants.VideoState.BUFFERING,
                PlayerConstants.VideoState.PLAYING_BACK,
                    -> {
                    mViewBinding.detailShVideo.pause()
                    mVideoHuaPingComponent?.show()
                }
            }
        }
    }


    /**
     * 开始播放
     * drm 鉴权完成
     * setPlayInfo
     * 花屏全屏
     * dlna
     */
    private fun prepareVideo() {
        isChangeResolution = false
        AppLogger.d(TAG, "起播prepareVideo")
        //设置播放数据
        mShDataSource.destroyAdLoader = true
        mViewBinding.detailShVideo.setDataSource(mShDataSource)
        VideoStartTimeUtil.getInstants()
            .initVideoInfo("Video Name:${mVideoInfoResponse?.videoName} aid:$aid vid:$vid  videoType:$dataType  isTry:$isTryVideo isLogin:$hasLogin isVip:$hasVip drmType:${mBaseOptionModel.drmType}")
        //异步起播
        mViewBinding.detailShVideo.prepareAsync()
        mShDataSource.videoCateCodeId = mVideoPlayInfoResponse?.cateId?.toLong() ?: 0L
        setVvPageId()
        //点播放按钮时发送
        VvPushManger.getInstance().onVVCreate(mShDataSource)
    }

    private fun setVvPageId() {
        VvConfig.vvParamsItem?.enterPageid = sourceId.toString()
        VvConfig.vvParamsItem?.isTry = isTryVideo
        VvConfig.vvParamsItem?.drmType = getServiceTvPlayType()
    }

    /**
     * Vv清晰度
     */
    private fun onVvResolution(resolution: Resolution) {
        VvPushManger.getInstance()
            .setVVResolution(resolution)
    }

    /**
     * 开始播放视频内容时发送（剧集播放1秒后发送）
     */
    private fun onVvStart() {
        VvPushManger.getInstance().onStart()
    }

    /**
     * 播放完发送
     */
    private fun onVvComplete() {
        VvPushManger.getInstance().onComplete()
    }

    /**
     * 关闭时发送
     */
    private fun onVvClose() {
        VvPushManger.getInstance().onClose()
    }

    /**
     * 当前选中的只看他数据
     * null为全部
     */
    private var mCurrentOnlySeeMenuItem: ScaleContentOnlySeeMenuItem? = null
    private var mProSeeList: MutableList<SoHuVideoProgressBar.SecondListBean> = mutableListOf()
    private var mOnlySeeList: MutableList<ScaleContentOnlySeeMenuItem> = mutableListOf()
    private var mVideoInfoOnlySeeDatas: MutableList<VideoInfoOnlySeeItem>? = mutableListOf()


    /**
     * 转换并查找只看他数据 并设置起始位置播放
     */
    private fun conversionOnlySeeData() {
        mVideoPlayInfoResponse?.lookHimSet?.conversionOnlySeeData(mCurrentOnlySeeMenuItem) { videoInfoOnlySeeDatas, isOnlySee, isFind, findOnlySeeMenuItem, proSeeList, onlySeeList ->
            this.haveOnlySee = isOnlySee
            mVideoInfoOnlySeeDatas = videoInfoOnlySeeDatas
            mProSeeList.clear()
            mOnlySeeList.clear()
            mProSeeList.addAll(proSeeList)
            mOnlySeeList.addAll(onlySeeList)
            mCurrentOnlySeeMenuItem = if (isOnlySee && isFind) {
                findOnlySeeMenuItem
            } else {
                if (isOnlySeeModel) {
                    //没有找到对应片段  恢复状态及ui
                    ToastUtils.showLongToast(
                        this,
                        "本集无" + mCurrentOnlySeeMenuItem?.name + "，已为您播放完整版"
                    )
                }
                null
            }
            setCurrentOnlySeeMenuItem()
            var segments: MutableList<Long>? = null
            mCurrentOnlySeeMenuItem?.let {
                segments = mutableListOf()
                it.timeArray?.forEach { time ->
                    segments?.add(
                        AdTsManger.getInstants().getCalculateOnlySeeTime(time.start) * 1000
                    )
                    segments?.add(AdTsManger.getInstants().getCalculateOnlySeeTime(time.end) * 1000)
                }
                if (mVideoPlayInfoResponse?.isLastEpisode == true && segments!!.size > 0 && (mVideoPlayInfoResponse?.videoLength
                        ?: 0) > segments!![(segments!!.size - 1)] / 1000
                ) {
                    mVideoPlayInfoResponse?.videoLength?.toLong()?.let {
                        segments!!.removeAt((segments!!.size - 1))
                        segments!!.add(it * 1000)
                    }
                }

            }
            mBaseOptionModel.segments = segments
            mBaseOptionModel.segmentLen = mCurrentOnlySeeMenuItem?.timeArray?.size ?: 0
            mBaseOptionModel.segmentStartIndex = onlySeeIndex
        }
    }

    /**
     * 影片是否有只看他数据
     */
    private var haveOnlySee = false

    /**
     * 当前是否是只看他模式
     */
    private val isOnlySeeModel
        get() = mCurrentOnlySeeMenuItem != null && mCurrentOnlySeeMenuItem?.id?.equals("0") == false

    //只看他当前播放位置
    private var onlySeeIndex = 0

    /**
     * 设置只看他数据
     */
    private fun setCurrentOnlySeeMenuItem() {
        mVideoPlayMenuConfigComponent?.initData(haveOnlySee, mCurrentOnlySeeMenuItem)
        mVideoPlayControlComponent?.initTabData(haveOnlySee, mCurrentOnlySeeMenuItem)
        mVideoPlayMenuConfigComponent?.setOnlySee(mOnlySeeList)
        mVideoPlayControlComponent?.setProgressModel(isOnlySeeModel, mProSeeList)
        mVideoNormalProgressComponent?.setProgressModel(isOnlySeeModel, mProSeeList)
    }


    /**
     * 切换只看他功能
     */
    override fun onClickOnlySee(
        item: ScaleContentOnlySeeMenuItem,
        index: Int,
        isNeedDelayChangeNextSegments: Boolean,
    ) {
        mViewBinding.detailShVideo.resume()
        onlySeeIndex = index
        mCurrentOnlySeeMenuItem = item
        mCurrentOnlySeeMenuItem.findOnlySeeDataItem(
            mVideoInfoOnlySeeDatas!!
        ) { isFind, findOnlySeeMenuItem, proSeeList, onlySeeList ->
            mProSeeList.clear()
            mOnlySeeList.clear()
            mProSeeList.addAll(proSeeList)
            mOnlySeeList.addAll(onlySeeList)
            mCurrentOnlySeeMenuItem = findOnlySeeMenuItem
            setCurrentOnlySeeMenuItem()
            showPlayControl()
            if (isOnlySeeModel) {
                if (isNeedDelayChangeNextSegments) {
                    return
                }
                var segments: MutableList<Long>? = null
                mCurrentOnlySeeMenuItem?.let {
                    segments = mutableListOf()
                    it.timeArray?.forEach { time ->
                        segments?.add(
                            AdTsManger.getInstants().getCalculateOnlySeeTime(time.start * 1000)
                        )
                        segments?.add(
                            AdTsManger.getInstants().getCalculateOnlySeeTime(time.end * 1000)
                        )
                    }
                    //最后一集 videoLength>segments last end
                    if (mVideoPlayInfoResponse?.isLastEpisode == true && segments!!.size > 0 && (mVideoPlayInfoResponse?.videoLength
                            ?: 0) > segments!![(segments!!.size - 1)] / 1000
                    ) {
                        mVideoPlayInfoResponse?.videoLength?.toLong()?.let {
                            segments!!.removeAt((segments!!.size - 1))
                            segments!!.add(it * 1000)
                        }
                    }
                }
                mViewBinding.detailShVideo.setSegments(
                    segments,
                    mCurrentOnlySeeMenuItem?.timeArray?.size ?: 0,
                    onlySeeIndex
                )
            } else {
                onlySeeIndex = 0
                mViewBinding.detailShVideo.closedSegments()
            }
        }
    }

    //跳过片头片尾
    override fun onSkipStartAndEnd() {
        skipStartTimeNow()
    }


    //logo 展示信息
    private var logoInfo: LogoInfo? = null

    /**
     * 获取当前清晰度
     */


    /**
     * 获取当前清晰度
     */
    private fun getCurrentPlayResolution(): Int {
        return if (isStartLoginResolutionApp) {
            startLoginResolution?.id ?: 1
        } else {
            if (isDlna) {
                findDlnaResolutionApp(mDlnaMode?.definition)
            } else {
                if (currentResolutionInfo != null) {
                    currentResolutionInfo?.id!!
                } else {
                    if (AppConfigDatabase.getUserBeforeResolutionId() != -1) {
                        AppConfigDatabase.getUserBeforeResolutionId()
                    } else {
                        AppConfigDatabase.getDefaultResolutionId()
                    }
                }
            }
        }
    }

    var currentResolutionInfo: ResolutionInfo? = null
        set(value) {
            value?.id?.let {
                AppConfigDatabase.putUserBeforeResolutionId(it)
            }
            field = value
        }

    private fun conversionResolution(
    ) {
        val default = mVideoPlayInfoResponse?.resolutionInfos?.find { it.default == true }
        AppLogger.d(
            TAG,
            "setPlayInfo fun conversionResolution default is :${default?.toString()}  ${hasVip} ${hasLogin}"
        )
        mVideoPlayInfoResponse?.resolutionInfos?.conversionResolution(
            getCurrentPlayResolution(),
            hasVip,
            hasLogin,
            action = { info ->
                currentResolutionInfo = info
                AppLogger.d(TAG, "setPlayInfo info  action  is :${info?.toString()} ")
                PlaySettingHelper.setPlayClarity(info?.id ?: 1)

                mCurrentUrl = info?.videoPlayInfo?.url
//                //试看时drm外采不吐 url处理 因版权问题
//                if (getServiceTvPlayType() == 3 && mCurrentUrl.isNullOrEmpty()) {
//                    mCurrentUrl = info?.videoPlayInfo?.tryPlayUrl
//                }
                mVideoLogoComponent?.setLogoInfo(logoInfo)
                if (dataType == Constant.DATA_TYPE_VRS) {
                    info?.videoPlayInfo?.hasLogo?.let {
                        mVideoLogoComponent?.setChangeResolutionEnableLogo(it)
                    }
                }
                mVideoPlayMenuConfigComponent?.setClarity(
                    mVideoPlayInfoResponse?.resolutionInfos!!,
                    aid,
                    vid
                )
                mVideoPlayMenuConfigComponent?.setCurrentClarity(info!!)
                setHdrInfo()

                AdTsManger.getInstants().setVideoClarity(
                    info!!.findPlayerResolution()
                )
                onVvResolution(info!!.findPlayerResolution())
                sendPlayDefinition(info)
            })
    }

    private fun setHdrInfo() {
        val hdr =
            mVideoPlayInfoResponse?.resolutionInfos?.find { it.id == ResolutionApp.APP_ORIGINAL_HDR.appValue }
        mVideoHdrDetailComponent?.setCurrentClarity(hdr)
    }

    private fun requestModel() {
        mVideoDetailViewModel.onStartLoad(
            passport = passport,
            token = loginToken,
            videoType = dataType,
            aid = aid,
            gid = gid
        )
    }

    /**
     * 设置dlna 投屏数据
     */
    private fun setDlnaData() {
        AppLogger.d(TAG, "setDlnaData is dlna ${isDlna}")
        if (isDlna) {
            setDlnaModel()
            setDlnaIntentData()
        }
    }

    /**
     *  设置是否是来源于搜狐视频
     */
    private fun setDlnaModel() {
        SohuDlnaManger.getInstance().setIsSohuVideo(mDlnaMode?.isSohuVideo ?: false)
        try {
            PlaySettingHelper.setDlnaPlayClarity(
                mDlnaMode?.definition?.toInt() ?: ResolutionApp.APP_HIGH.appValue
            )
        } catch (e: Throwable) {
            PlaySettingHelper.setDlnaPlayClarity(
                ResolutionApp.APP_HIGH.appValue
            )
            AppLogger.d(TAG, "setDlnaModel setDlnaPlayClarity error  is :${e.localizedMessage}")
            AppLogger.d(
                TAG,
                "setDlnaModel setDlnaPlayClarity fun error definition is :${mDlnaMode?.definition}"
            )
        }
    }

    /**
     * dlna 发送当前播放清晰度
     */
    private fun sendPlayDefinition(finderInfo: ResolutionInfo?) {
        val dlnaResolution = ResolutionServer.values()?.find {
            finderInfo?.videoPlayInfo?.serviceVersionId == it.serverValue264 || finderInfo?.videoPlayInfo?.serviceVersionId == it.serverValue265
        }
        SohuDlnaManger.getInstance().sendPlayDefinition(
            dlnaResolution?.serverValue264 ?: ResolutionServer.MEDIA_HIGH.serverValue264
        )
    }


    /**
     * 来源搜狐视频需要 拿到 vid aid 视频类型
     */
    private fun setDlnaIntentData() {
        val valuer: Any? = intent.getParcelableExtra("mDlnaMode")
        if (valuer is DlnaMediaModel) {
            mDlnaMode = valuer
            if (mDlnaMode?.vid?.isEmpty() == true) {
                vid = -1
            } else {
                vid = mDlnaMode?.vid?.toInt() ?: -1
                aid = mDlnaMode?.aid?.toInt() ?: -1
                dataType =
                    if (mDlnaMode?.isPgc == true) Constant.DATA_TYPE_PGC else Constant.DATA_TYPE_VRS
            }
        }
    }

    private fun showVideoView() {
        if (isDlna) {
            mViewBinding.detailLoadingView.hide()
            mViewBinding.detailErrorLayout.errView.gone()
            mViewBinding.detailShVideo.visible()
            mViewBinding.detailVideoInfo.gone()
            mViewBinding.detailShHeaderLayout.gone()
            mViewBinding.detailRecommendList.gone()
        } else {
            mViewBinding.detailLoadingView.hide()
            mViewBinding.detailErrorLayout.errView.gone()
            mViewBinding.detailShVideo.visible()
            mViewBinding.detailVideoInfo.visible()
            mViewBinding.detailShHeaderLayout.visible()
            mViewBinding.detailRecommendList.visible()
        }
    }

    /**
     *  不来源与搜狐视频的投屏 播放
     *
     */
    private fun dlnaNormalPlay() {

        startVideoWindowFull()
        mCurrentUrl = mDlnaMode?.url
        mShDataSource.url = mCurrentUrl
        mBaseOptionModel.also {
            it.startPosition = 0
            it.endPosition = 0
        }
        setVideoOption()
        mShDataSource.adSkip = true
        mShDataSource.also {
            it.gid = UserInfoHelper.getGid() ?: ""
        }
        AppLogger.d(TAG, "dlnaNormalPlay url is $mCurrentUrl")
        //异步起播
        prepareVideo()
    }

    /**
     * 更新剩余时间
     */
    private fun updateRemainingTime() {
        val isShowTime = mVideoPayInfoResponse?.isShowTime ?: false
        if (windowFloat) {
            if (mViewBinding.tvVideoDetailHeaderRemainingTime.visibility == ConstraintLayout.INVISIBLE) return
            mViewBinding.tvVideoDetailHeaderRemainingTime.invisible()
        } else {
            if (mViewBinding.tvVideoDetailHeaderRemainingTime.visibility == if (isShowTime) ConstraintLayout.VISIBLE else ConstraintLayout.INVISIBLE) return
            mViewBinding.tvVideoDetailHeaderRemainingTime.isInvisible(isShowTime)
        }
    }


    //更新显示剩余时间
    private fun updateTimer(time: Long) {
        val hours = time / (1000 * 60 * 60)
        val minutes = time % (1000 * 60 * 60) / (1000 * 60)
        mViewBinding.tvVideoDetailHeaderRemainingTime.text =
            resources.getString(R.string.video_detail_expire_time) +
                    "：" + String.format("%02d", hours) + "小时" + String.format(
                "%02d",
                minutes
            ) + "分"
    }


    private inner class MyCountDownTimer(millisInFuture: Long) :
        CountDownTimer(millisInFuture, 30000) {
        override fun onTick(time: Long) {
            updateTimer(time)
        }

        override fun onFinish() {

        }
    }

    /**
     * 更新底部按钮组的展示隐藏
     */
    private fun updateBottomButtonVisible() {
        //青少年只有全屏
        if (hasTeenager) {
            mViewBinding.btnVideoDetailHeaderPay.gone()
            mViewBinding.btnVideoDetailHeaderCollection.gone()
            mViewBinding.btnVideoDetailHeaderMembers.gone()
            mViewBinding.btnVideoDetailHeaderFull.requestFocus()
            return
        } else {
            //pgc 不显示收藏
            if (dataType != Constant.DATA_TYPE_VRS) {
                mViewBinding.btnVideoDetailHeaderCollection.gone()
            } else {
                mViewBinding.btnVideoDetailHeaderCollection.visible()
            }
            mViewBinding.btnVideoDetailHeaderMembers.visible()
        }

        //版权到期不展示购买按钮
        if (TextUtils.equals(
                mViewBinding.tvVideoDetailHeaderRemainingTime.text,
                "版权到期，无法购买"
            )
        ) {
            mViewBinding.btnVideoDetailHeaderPay.gone()
            return
        }

        //已经登录
        if (hasLogin) {
            //是会员
            if (hasVip) {
                if (isMember == true && isSingle == true) {
                    //隐藏购买按钮
                    mViewBinding.btnVideoDetailHeaderPay.gone()
                    //已存在按钮焦点刷新时不重新去请求焦点  避免焦点发生跳转
                    if (hasRequestFocus()) return
                    //默认焦点在全屏
                    mViewBinding.btnVideoDetailHeaderFull.requestFocus()
                    return
                }
                //用券
                if (isTicket == true) {
                    //显示购买按钮
                    if (mVideoPayInfoResponse?.isShowTime == false) {
                        mViewBinding.btnVideoDetailHeaderPay.visible()
                        pushPayEvent()
                    }
                    //已存在按钮焦点刷新时不重新去请求焦点  避免焦点发生跳转
                    if (hasRequestFocus()) return
                    //默认焦点在购买
                    mViewBinding.btnVideoDetailHeaderPay.requestFocus()
                    return
                }
                //影片是会员、
                if (isMember == true) {
                    //隐藏购买按钮
                    mViewBinding.btnVideoDetailHeaderPay.gone()
                    //已存在按钮焦点刷新时不重新去请求焦点  避免焦点发生跳转
                    if (hasRequestFocus()) return
                    //默认焦点在全屏
                    mViewBinding.btnVideoDetailHeaderFull.requestFocus()
                    return
                }
                //单点
                if (isSingle == true) {
                    //显示购买按钮
                    if (mVideoPayInfoResponse?.isShowTime == false) {

                        mViewBinding.btnVideoDetailHeaderPay.visible()
                        pushPayEvent()
                    }

                    //已存在按钮焦点刷新时不重新去请求焦点  避免焦点发生跳转
                    if (hasRequestFocus()) return
                    //默认焦点在购买
                    mViewBinding.btnVideoDetailHeaderPay.requestFocus()
                    return
                }
                mViewBinding.btnVideoDetailHeaderPay.gone()
                //已存在按钮焦点刷新时不重新去请求焦点  避免焦点发生跳转
                if (hasRequestFocus()) return
                mViewBinding.btnVideoDetailHeaderFull.requestFocus()

            } else {
                if (isMember == true && isSingle == true) {
                    //隐藏购买按钮
                    if (mVideoPayInfoResponse?.isShowTime == false) {
                        mViewBinding.btnVideoDetailHeaderPay.visible()
                        pushPayEvent()
                    }

                    //已存在按钮焦点刷新时不重新去请求焦点  避免焦点发生跳转
                    if (hasRequestFocus()) return
                    //默认焦点在购买
                    mViewBinding.btnVideoDetailHeaderPay.requestFocus()
                    return
                }

                //影片是会员
                if (isMember == true || isSingle == true) {
                    //显示购买按钮
                    if (mVideoPayInfoResponse?.isShowTime == false) {
                        mViewBinding.btnVideoDetailHeaderPay.visible()
                        pushPayEvent()
                    }

                    //已存在按钮焦点刷新时不重新去请求焦点  避免焦点发生跳转
                    if (hasRequestFocus()) return
                    //默认焦点在购买
                    mViewBinding.btnVideoDetailHeaderPay.requestFocus()
                    return
                }
                mViewBinding.btnVideoDetailHeaderPay.gone()
                //已存在按钮焦点刷新时不重新去请求焦点  避免焦点发生跳转
                if (hasRequestFocus()) return
                mViewBinding.btnVideoDetailHeaderFull.requestFocus()
            }

        } else {
            if (isMember == true && isSingle == true) {
                //隐藏购买按钮
                if (mVideoPayInfoResponse?.isShowTime == false) {
                    mViewBinding.btnVideoDetailHeaderPay.visible()
                    pushPayEvent()
                }

                //已存在按钮焦点刷新时不重新去请求焦点  避免焦点发生跳转
                if (hasRequestFocus()) return
                //默认焦点在购买
                mViewBinding.btnVideoDetailHeaderPay.requestFocus()
                return
            }
            //影片是会员或者单点或者用卷
            if (isMember == true || isSingle == true || isTicket == true) {
                //显示购买按钮
                if (mVideoPayInfoResponse?.isShowTime == false) {
                    mViewBinding.btnVideoDetailHeaderPay.visible()
                    pushPayEvent()
                }

                //已存在按钮焦点刷新时不重新去请求焦点  避免焦点发生跳转
                if (hasRequestFocus()) return
                //默认焦点在购买
                mViewBinding.btnVideoDetailHeaderPay.requestFocus()
                return
            }
            mViewBinding.btnVideoDetailHeaderPay.gone()
            //已存在按钮焦点刷新时不重新去请求焦点  避免焦点发生跳转
            if (hasRequestFocus()) return
            mViewBinding.btnVideoDetailHeaderFull.requestFocus()
        }


    }

    private fun updateHeaderLayout() {
        val layoutParams = mViewBinding.detailShHeaderLayout.layoutParams
        layoutParams.height = getIsFloatingHeight()
        mViewBinding.detailShHeaderLayout.layoutParams = layoutParams
    }


    private fun getIsFloatingHeight(): Int {
        return if (windowFloat) {
            resources.getDimension(R.dimen.y372).toInt()
        } else {
            resources.getDimension(R.dimen.y664).toInt()
        }
    }

    private fun updateHeaderWindow() {
        setBriefIntroductionBackground(windowFloat)
        setRootWindowBackground()
        updateHeaderLayout()
        mViewBinding.llVideoDetailHeaderBottomButton.isGone(windowFloat)
        updateBriefIntroduction()
    }

    /**
     * 更新简介
     */
    private fun updateBriefIntroduction() {
        //设置最大行数
        val maxLines: Int = mVideoInfoResponse?.decMaxLine ?: 2
        mViewBinding.tvFloatingBriefIntroduction.maxLines = maxLines
        //动态设置的目的是为了  与 另一个textview 对齐 且 三行文字时不会显示不全
        if (maxLines == 3) {
            mViewBinding.tvFloatingBriefIntroduction.setLineSpacing(
                resources.getDimension(R.dimen.x20),
                1.0f
            )
        } else {
            mViewBinding.tvFloatingBriefIntroduction.setLineSpacing(
                resources.getDimension(R.dimen.x20),
                1.0f
            )
        }
        //设置文本
        var firstDesc = mVideoInfoResponse?.firstDesc ?: ""
        var secondDesc = mVideoInfoResponse?.secondDesc ?: ""
        val spannableStringBuilder = SpannableStringBuilder()
        if (!windowFloat) {
            spannableStringBuilder.append(secondDesc)
            spannableStringBuilder.setSpan(
                ForegroundColorSpan(
                    ContextCompat.getColor(
                        this,
                        R.color.tv_color_99e8e8ff
                    )
                ),
                if (firstDesc.length > 0) firstDesc.length - 1 else 0,
                secondDesc.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            mViewBinding.tvFloatingBriefIntroduction.text = spannableStringBuilder
            mViewBinding.clScaleBriefIntroduction.isVisible(secondDesc.trim().isNotEmpty())
        } else {
            mViewBinding.tvFloatingBriefIntroduction.text = firstDesc
            mViewBinding.clScaleBriefIntroduction.isVisible(firstDesc.trim().isNotEmpty())
        }

        //设置省略号
        var mBriefIntroductionViewTreeObserver =
            mViewBinding.tvFloatingBriefIntroduction.viewTreeObserver
        mBriefIntroductionViewTreeObserver!!.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                mBriefIntroductionViewTreeObserver!!.removeOnGlobalLayoutListener(this)
                val detailMoreWidth = resources.getDimensionPixelSize(R.dimen.x80)
                val lines = mViewBinding.tvFloatingBriefIntroduction.lineCount
                if (lines < 2 || windowFloat) {
                    mViewBinding.tvFloatingBriefIntroductionTip.gone()
                    mViewBinding.ivFloatingBriefIntroductionTip.gone()
                    mViewBinding.clScaleBriefIntroduction.isClickable = false
                } else {
                    mViewBinding.tvFloatingBriefIntroductionTip.visible()
                    mViewBinding.ivFloatingBriefIntroductionTip.visible()

                    mViewBinding.clScaleBriefIntroduction.isClickable = true
                }
                if (mViewBinding.tvFloatingBriefIntroduction.lineCount > maxLines) {
                    //判断行数大于多少时改变
                    val firsLineIndex: Int =
                        mViewBinding.tvFloatingBriefIntroduction.layout.getLineEnd(0)
                    val lineEndIndex: Int =
                        mViewBinding.tvFloatingBriefIntroduction.layout.getLineEnd(maxLines - 1) //设置第2行打省略号
                    var newEndIndex = lineEndIndex - 6
                    val str: String = mViewBinding.tvFloatingBriefIntroduction.text.toString()
                    for (i in lineEndIndex - 3 downTo 0) {
                        val width = Util.getTextViewStrWidth(
                            str.substring(i, lineEndIndex),
                            mViewBinding.tvFloatingBriefIntroduction.paint
                        )
                        val offsetWidth = detailMoreWidth + Util.getTextViewStrWidth(
                            "...",
                            mViewBinding.tvFloatingBriefIntroduction.paint
                        )
                        if (width != -1 && detailMoreWidth != -1 && width >= offsetWidth) {
                            newEndIndex = i
                            break
                        }
                    }
                    if (newEndIndex < firsLineIndex) {
                        newEndIndex = lineEndIndex - 1
                    }
                    val spannableStringBuilder = SpannableStringBuilder()
                    val text: String =
                        mViewBinding.tvFloatingBriefIntroduction.text.subSequence(
                            0,
                            newEndIndex
                        )
                            .toString() + "..."
                    spannableStringBuilder.append(text)
                    if (firstDesc.length - 1 < text.length) {
                        spannableStringBuilder.setSpan(
                            ForegroundColorSpan(
                                ContextCompat.getColor(
                                    this@VideoActivity,
                                    R.color.tv_color_99e8e8ff
                                )
                            ),
                            if (firstDesc.length > 0) firstDesc.length - 1 else 0,
                            text.length,
                            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    }
                    mViewBinding.tvFloatingBriefIntroduction.text = spannableStringBuilder
                }

            }


        })

//        }

    }

    /**
     * 根据是否是悬浮窗 设置header 背景
     * @param isFloating 是否显示悬浮窗的情况
     */
    private fun setRootWindowBackground() {
        if (windowFloat) {
            mViewBinding.detailShHeaderLayout.background = ContextCompat.getDrawable(
                this,
                R.drawable.bg_video_detail_header_floating_view
            )
        } else {
            mViewBinding.detailShHeaderLayout.setBackgroundColor(
                ContextCompat.getColor(
                    this,
                    R.color.transparent
                )
            )
        }
    }

    //加载会员活动图片
    private fun loadMembers(url: String?) {
        //过度和错误默认图

        GlideApp.with(this)
            .load(url)
            .error(R.mipmap.ic_members_error)
            .placeholder(mViewBinding.ivVideoDetailHeaderMembers.drawable)
            .fitCenter()
            .transform(RoundedCorners(resources.getDimensionPixelOffset(R.dimen.x10)))
            .into(mViewBinding.ivVideoDetailHeaderMembers);
    }

    /**
     * 设置简介的背景  根据是否是悬浮窗改变
     */
    private fun setBriefIntroductionBackground(isFloating: Boolean) {
        val briefIntroductionBg = if (isFloating) {
            null
        } else {
            ContextCompat.getDrawable(
                this,
                R.drawable.bg_gradual_change_start_2b2b42_end_4b4a5c_radius_10_angle_0
            )
        }
        mViewBinding.clScaleBriefIntroduction.background = briefIntroductionBg
        mViewBinding.clScaleBriefIntroduction.minHeight =
            if (isFloating) 0 else LayoutParams.WRAP_CONTENT
        mViewBinding.tvFloatingBriefIntroductionTip.paint.isFakeBoldText = true
        mViewBinding.tvFloatingBriefIntroductionTip.isGone(isFloating)
        mViewBinding.ivFloatingBriefIntroductionTip.isGone(isFloating)
        val left = if (isFloating) 0 else resources.getDimension(R.dimen.x24).toInt()
        val top = if (isFloating) 0 else resources.getDimension(R.dimen.x20).toInt()
        val bottom = if (isFloating) 0 else resources.getDimension(R.dimen.x20).toInt()
        val end = if (isFloating) 0 else resources.getDimension(R.dimen.x20).toInt()
        mViewBinding.tvFloatingBriefIntroduction.setPadding(
            left, top, end, bottom
        )

    }

    /**
     * 是否是 已经有焦点
     * 当 购买或者会员活动持有焦点时  刷新数据是不再请求全屏焦点
     *
     */
    private fun hasRequestFocus(): Boolean {
        if (mViewBinding.btnVideoDetailHeaderPay.hasFocus()) {
            updatePayFocusChange(mViewBinding.btnVideoDetailHeaderPay, true)
            return true
        }
        if (mViewBinding.btnVideoDetailHeaderMembers.hasFocus()) {
            updateMembersFocusChange(mViewBinding.btnVideoDetailHeaderMembers, true)
            return true
        }
        if (mViewBinding.btnVideoDetailHeaderFull.hasFocus()) {
            updateFullFocusChange(mViewBinding.btnVideoDetailHeaderFull, true)
            return true
        }
        if (mViewBinding.clScaleBriefIntroduction.hasFocus()) {
            updateBriefIntroductionFocusChange(mViewBinding.clScaleBriefIntroduction, true)
            return true
        }
        if (mViewBinding.headerViewVideoDetailUser.hasFocus()) {
            return true
        }
        return false
    }

    //简介
    private fun updateBriefIntroductionFocusChange(focusView: View, hasFocus: Boolean) {
        if (hasFocus) {
            focusView.background =
                getDrawableOfId(R.drawable.bg_gradual_change_start_2b2b42_end_4b4a5c_radius_10_stroke_3_color_fffff)
        } else {
            focusView.background =
                getDrawableOfId(R.drawable.bg_gradual_change_start_2b2b42_end_4b4a5c_radius_10_angle_0)
        }

    }


    /**
     * 更新购买按钮的焦点变化处理
     */
    private fun updatePayFocusChange(view: View, hasFocus: Boolean) {
        if (hasFocus) {
            view.scaleXY(1.1f)
            mViewBinding.tvVideoDetailHeaderPay.setTextColor(getColorOfId(R.color.tv_color_692910))
            mViewBinding.ivVideoDetailHeaderPay.setImageDrawable(getDrawableOfId(R.mipmap.ic_pay_selected))
            view.background =
                getDrawableOfId(R.drawable.bg_video_detail_header_floating_members_gradual_change)
        } else {
            view.scaleXY(1f)
            mViewBinding.ivVideoDetailHeaderPay.setImageDrawable(getDrawableOfId(R.mipmap.ic_pay_no_selected))
            mViewBinding.tvVideoDetailHeaderPay.setTextColor(getColorOfId(R.color.tv_color_e7b99D))
            view.background =
                getDrawableOfId(R.drawable.bg_gradual_change_start_2b2b42_end_4b4a5c_radius_10_angle_0)
        }

    }


    //会员活动
    private fun updateMembersFocusChange(focusView: View, hasFocus: Boolean) {
        if (hasFocus) {
            loadMembers(configInfo?.vipBtn?.focus ?: "")
            focusView.scaleXY(1.1f)
        } else {
            loadMembers(configInfo?.vipBtn?.blur ?: "")
            focusView.scaleXY(1f)
        }

    }


    //收藏
    private fun updateCollectionFocusChange(focusView: View, hasFocus: Boolean) {
        if (hasFocus) {
            focusView.background =
                getDrawableOfId(R.drawable.bg_select_focus_e4705c_radius_11)
            focusView.scaleXY(1.1f)
        } else {
            focusView.background =
                getDrawableOfId(R.drawable.bg_gradual_change_start_2b2b42_end_4b4a5c_radius_10_angle_0)
            focusView.scaleXY(1f)
        }
        setCollectionStateImg()
    }

    /**
     * 设置收藏的图片资源
     */
    private fun setCollectionStateImg() {
        if (enableCollect) {
            if (mViewBinding.btnVideoDetailHeaderCollection.hasFocus()) {
                mViewBinding.ivVideoDetailHeaderCollection.setImageDrawable(getDrawableOfId(R.mipmap.ic_already_collection_selected))
            } else {
                mViewBinding.ivVideoDetailHeaderCollection.setImageDrawable(getDrawableOfId(R.mipmap.ic_already_collection_not_selected))
            }
        } else {
            if (mViewBinding.btnVideoDetailHeaderCollection.hasFocus()) {
                mViewBinding.ivVideoDetailHeaderCollection.setImageDrawable(getDrawableOfId(R.mipmap.ic_collection_selected))
            } else {
                mViewBinding.ivVideoDetailHeaderCollection.setImageDrawable(getDrawableOfId(R.mipmap.ic_collection_not_selected))
            }
        }
        mViewBinding.tvVideoDetailHeaderCollection.text =
            if (enableCollect) "已收藏" else "收藏"
    }

    /**
     * 更新全屏焦点变化处理
     */
    private fun updateFullFocusChange(view: View, hasFocus: Boolean) {
        if (hasFocus) {
            view.scaleXY(1.1f)
            mViewBinding.tvVideoDetailHeaderFull.setTextColor(getColorOfId(R.color.tv_color_ffffff))
            mViewBinding.ivVideoDetailHeaderFull.setImageDrawable(getDrawableOfId(R.mipmap.is_full_selected))
            mViewBinding.btnVideoDetailHeaderFull.background =
                getDrawableOfId(R.drawable.bg_select_focus_e4705c_radius_11)
        } else {
            view.scaleXY(1f)
            mViewBinding.tvVideoDetailHeaderFull.setTextColor(getColorOfId(R.color.tv_color_b3e8e8ff))
            mViewBinding.ivVideoDetailHeaderFull.setImageDrawable(getDrawableOfId(R.mipmap.ic_full_not_selected))
            mViewBinding.btnVideoDetailHeaderFull.background =
                getDrawableOfId(R.drawable.bg_gradual_change_start_2b2b42_end_4b4a5c_radius_10_angle_0)
        }
    }

    override fun onFocusChange(foucusView: View?, hasFocus: Boolean) {
        when (foucusView) {
            //全屏
            mViewBinding.btnVideoDetailHeaderFull -> {
                updateFullFocusChange(foucusView, hasFocus)
            }
            //购买
            mViewBinding.btnVideoDetailHeaderPay -> {
                updatePayFocusChange(foucusView, hasFocus)
            }
            //会员活动
            mViewBinding.btnVideoDetailHeaderMembers -> {
                updateMembersFocusChange(foucusView, hasFocus)

            }
            //收藏
            mViewBinding.btnVideoDetailHeaderCollection -> {
                updateCollectionFocusChange(foucusView, hasFocus)

            }
            //简介
            mViewBinding.clScaleBriefIntroduction -> {
                updateBriefIntroductionFocusChange(foucusView, hasFocus)
            }
        }
    }

    private fun pushPayEvent() {
        addPushEvent(10309, IMP, pathInfo = getInfoEvent {
            it["pageId"] = 1041
        }, null, null)
    }


    private fun getDrawableOfId(id: Int): Drawable? {
        return ContextCompat.getDrawable(this, id)
    }

    private fun getColorOfId(id: Int): Int {
        return ContextCompat.getColor(this, id)
    }

    /**
     * 是否请求暂停广告
     */
    @Volatile
    private var isRequestPauseAd = false
        set(value) {
            mVideoStartPreparingComponent?.isPauseAd = value
            field = value
        }

    private fun stopPauseAd() {
        stopPauseAd(true)
    }

    /**
     * 关闭暂停广告
     */
    @Synchronized
    private fun stopPauseAd(isReport: Boolean = true) {
        if (!isRequestPauseAd) return
        mViewBinding.detailShVideo.stopPauseAdVideo()
        hidPauseAd(isReport)
        isRequestPauseAd = false
    }

    private fun hidPauseAd(): Boolean {
        return hidPauseAd(true)
    }

    /**
     *  隐藏暂停广告
     */
    private fun hidPauseAd(isReport: Boolean = false): Boolean {
        if (mVideoAdPauseFilmComponent?.hasShow() == true) {
            mVideoAdPauseFilmComponent?.hide(isReport)
            return true
        }
        if (mVideoAdPauseImageComponent?.hasShow() == true) {
            mVideoAdPauseImageComponent?.hide(isReport)
            return true
        }
        return false
    }

    /**
     * 开始请求暂停广告
     */
    @Synchronized
    private fun startPauseAd() {
        //dlna 拦截
        if (isDlna) return
        if (isRequestPauseAd) return
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) return  //小于5.0系统，暂不支持暂停MAX广告
        mVideoAdPauseImageComponent?.posterUrl = mVideoPlayInfoResponse?.videoExtendsPic_640
        mVideoAdPauseFilmComponent?.posterUrl = mVideoPlayInfoResponse?.videoExtendsPic_640
        isRequestPauseAd = true
        mViewBinding.detailShVideo.requestPauseAd()
    }

    /**
     * 上次点击返回键的时间
     */
    private var lastBackPressTime = -1L
    private var oldFullFocus: View? = null
    override fun dispatchKeyEvent(event: KeyEvent?): Boolean {
        AppLogger.d(TAG, "dispatchKeyEvent fun screenMode:$screenMode")
        if (!isResume) return true
        if (isFull) {
            if (mVideoHdrDetailComponent?.visibility == View.VISIBLE) {
                mVideoHdrDetailComponent?.dispatchKeyEvent(event)
                return true
            }
            if (mVideoPlayMenuConfigComponent?.visibility == View.VISIBLE) {
                return mVideoPlayMenuConfigComponent?.dispatchKeyEvent(event)
                    ?: super.dispatchKeyEvent(event)
            }
            if (event?.action == KeyEvent.ACTION_DOWN) {
                when (event.keyCode) {
                    KeyEvent.KEYCODE_BACK -> {
                        if (isDlna) {
                            val currentTIme = System.currentTimeMillis()
                            if (lastBackPressTime == -1L || lastBackPressTime - currentTIme >= 3000) {
                                // 显示提示信息
                                lastBackPressTime = currentTIme
                                ToastUtils.showToast2(this, "再按一次退出投屏播放")
                                return true
                            } else {
                                SohuDlnaManger.finishPlayingVideoView(true)
                                return true
                            }
                        }
                        if (hidPauseAd()) {
                            stopPauseAd()
                            return true
                        }
                        if (isAllFull) {
                            finish()
                            return true
                        }
                        if (mVideoPlayControlComponent?.getIsShow() == true) {
                            mVideoPlayControlComponent?.hide()
                            return true
                        }
                        if (!huaPingCheck()) {
                            startVideo()
                        }
                        startVideoWindowNormal()
                        return true
                    }
                    //处理点击事件
                    KeyEvent.KEYCODE_ENTER,
                    KeyEvent.KEYCODE_DPAD_CENTER -> {

                        if (promptCopyComponentHasShow()) {
                            if (hasShowDlnaPermissionView() && isDlna) {
                                val map = HashMap<String, String>(1)
                                map["pageId"] = "1064"
                                RequestManager.getInstance()
                                    .onAllEvent(EventInfo(10310, "clk"), map, null, null)
                                setPayHintText(HINT_TEXT_DEFAULT)
                                SohuDlnaManger.finishPlayingVideoView(true)
                            }
                            return true
                        }
                        if (adPlayState == PlayerConstants.VideoState.PLAYING && !isRequestPauseAd || mVideoAdHeaderComponent?.isVisible == true && !adPlayState.playerIsNotStart()) {
                            val map = java.util.HashMap<String, String>(1)
                            map["pageId"] = "1045"
                            RequestManager.getInstance()
                                .onAllEvent(EventInfo(10292, "clk"), map, null, null)
                            showPayOrTicketDialog()
                            return true
                        }

                        if (enableTryView() && mVideoTrySeeComponent?.hasShow() == true) {
                            val map = HashMap<String, String>(1)
                            map["pageId"] = "1045"
                            RequestManager.getInstance()
                                .onAllEvent(EventInfo(10301, "clk"), map, null, null)
                            showPayOrTicketDialog()
                            return true
                        }
                        if (filmPlayState == PlayerConstants.VideoState.BUFFERED || filmPlayState == PlayerConstants.VideoState.PLAYING || filmPlayState == PlayerConstants.VideoState.PLAYING_BACK) {
                            showPlayControl()
                            pauseVideo()
                            startPauseAd()
                            return true
                        }
                        if (filmPlayState == PlayerConstants.VideoState.PAUSED) {
                            stopPauseAd(false)
                            showPlayControl()
                            startVideo()
                            return true
                        }
                        return super.dispatchKeyEvent(event)
                    }

                    KeyEvent.KEYCODE_DPAD_UP -> {
                        if (hidPauseAd()) {
                            stopPauseAd()
                            showPlayControl()
                            return true
                        }
                    }

                    KeyEvent.KEYCODE_DPAD_LEFT,
                    KeyEvent.KEYCODE_DPAD_RIGHT -> {

                        if (isFastForward()) {
                            stopPauseAd()
                            showPlayControl()

                            return mVideoPlayControlComponent?.dispatchKeyEvent(event)
                                ?: super.dispatchKeyEvent(event)
                        }
                    }

                    KeyEvent.KEYCODE_MENU,
                    KeyEvent.KEYCODE_DPAD_DOWN -> {
                        //dlna 拦截
                        if (isDlna && !isShDlnaVideo) {
                            return true
                        }
                        if (hidPauseAd()) {
                            stopPauseAd()
                        }
                        if (mVideoPlayMenuConfigComponent?.visibility != View.VISIBLE) {
                            mVideoPlayControlComponent?.hide()
                            mVideoPlayMenuConfigComponent?.show()
                            mVideoPlayMenuConfigComponent?.setDefaultFocus(
                                if (Constant.DATA_TYPE_VRS == dataType) vid else mVideoInfoResponse?.serviceVid
                                    ?: vid
                            )
                            return mVideoPlayMenuConfigComponent!!.dispatchKeyEvent(event)
                        }
                        return super.dispatchKeyEvent(event)
                    }
                }
            }

            if (event?.action == KeyEvent.ACTION_UP) {
                when (event?.keyCode) {

                    KeyEvent.KEYCODE_DPAD_LEFT,
                    KeyEvent.KEYCODE_DPAD_RIGHT -> {
                        if (isFastForward() && mVideoPlayControlComponent?.getIsShow() == true) {
                            return mVideoPlayControlComponent!!.dispatchKeyEvent(event)
                        }
                    }
                }
                return super.dispatchKeyEvent(event)
            }
        } else {
            val root = mViewBinding.root
            val view = root.findFocus()
            oldFullFocus = view
            if (event?.action == KeyEvent.ACTION_UP) {
                when (event?.keyCode) {
                    KeyEvent.KEYCODE_DPAD_UP -> {
                        AppLogger.d(
                            TAG,
                            "KEYCODE_DPAD_UP ${mViewBinding.detailShVideo.hasFocus()} : ${mViewBinding.detailShVideoStub.hasFocus()}"
                        )
                        if (mViewBinding.detailRecommendList.getEpisodeGroupLayout()
                                ?.hasFocus() == true || mViewBinding.detailShVideo.hasFocus()
                        ) {
                            normalVideoFocus = null
                            onScrollWindowChange(false)
                            AppLogger.d(TAG, "2 onScrollWindowChange(false)")
                        }
                    }

                    KeyEvent.KEYCODE_DPAD_LEFT,
                    KeyEvent.KEYCODE_DPAD_RIGHT -> {
                        if (mVideoPlayControlComponent?.visibility == View.VISIBLE && !adPlayState.playerIsPlaying()) {
                            return mVideoPlayControlComponent!!.dispatchKeyEvent(event)
                        }
                    }
                }
                return super.dispatchKeyEvent(event)
            }
            when (event?.keyCode) {

                KeyEvent.KEYCODE_BACK -> {
                    if (windowFloat) {
                        mViewBinding.btnVideoDetailHeaderFull.requestFocus()
                        onScrollWindowChange(false)
                        AppLogger.d(TAG, "3 onScrollWindowChange(false)")
                        return true
                    }
                    mViewBinding.detailShVideo.release(clearState = true, clearRender = true)
                }
                //处理点击事件
                KeyEvent.KEYCODE_ENTER,
                KeyEvent.KEYCODE_DPAD_CENTER -> {
                    when (view) {
                        //全屏
                        mViewBinding.detailShVideo,
                        mViewBinding.btnVideoDetailHeaderFull -> {
                            oldFullFocus = view
//                        //提示文案展示时全屏点击拦截
                            normalVideoFocus = view
                            startVideoWindowFull()
                            addPushEvent(10278, CLICK, pathInfo = getInfoEvent {
                                it["pageId"] = "1041"
                            }, null, null)
                            return true
                        }
                        //收藏
                        mViewBinding.btnVideoDetailHeaderCollection -> {
                            oldFullFocus = view
                            if (enableCollect) {
                                postVideoDetailCancleChase()
                            } else {
                                postVideoDetailChase()
                            }
                            AppLogger.v(TAG, "收藏")
                            addPushEvent(10281, CLICK, pathInfo = getInfoEvent {
                                it["pageId"] = "1041"
                            }, null, null)
                            return true
                        }
                        //简介
                        mViewBinding.clScaleBriefIntroduction -> {
                            oldFullFocus = view

                            if (!mViewBinding.clScaleBriefIntroduction.isClickable) {
                                return true
                            }
                            ActivityLauncher.startVideoDetailMoreActivity(
                                this,
                                mVideoInfoResponse,
                                mViewBinding.detailRecommendList.recommendDataBean
                            )
                            AppLogger.v(TAG, "简介")
                            addPushEvent(10276, CLICK, pathInfo = getInfoEvent {
                                it["pageId"] = "1041"
                            }, null, null)
                            return true
                        }
                        //会员活动
                        mViewBinding.btnVideoDetailHeaderMembers -> {
                            oldFullFocus = view


                            addPushEvent(10280, CLICK, pathInfo = getInfoEvent {
                                it["pageId"] = "1041"
                            }, null, null)
                            ActivityLauncher.startPayActivityWithAidVid(
                                this,
                                aid,
                                vid,
                                ParamConstant.PAGE_SOURCE_PLAYER,
                                PayActivity.PAY_SOURCE_DETAIL,
                                false
                            )

                            return true
                        }
                        //购买
                        mViewBinding.btnVideoDetailHeaderPay -> {
                            oldFullFocus = view
                            addPushEvent(10279, CLICK, pathInfo = getInfoEvent {
                                it["pageId"] = "1041"
                            }, null, memoInfo = getInfoEvent {
                                val isSingle = isSingle ?: false || isTicket ?: false
                                if (isMember && isSingle) {
                                    it["playlist_type"] = 2
                                } else
                                    if (isSingle) {
                                        it["playlist_type"] = 1
                                    } else if (isMember) {
                                        it["playlist_type"] = 0
                                    }
                                it["is_login"] = if (hasLogin) 1 else 0
                                it["is_vip"] = if (hasVip) 1 else 0
                            })
                            AppLogger.d(TAG, "购买")
                            onClickPayButton()
                            return true
                        }
                    }
                }

                KeyEvent.KEYCODE_DPAD_UP -> {
                    if (oldFullFocus?.id == R.id.episode_tab_select) {
                        mViewBinding.detailShVideo.requestFocus()
                        return true
                    }
                    if (oldFullFocus?.id == R.id.episode_tab_series) {
                        mViewBinding.detailShVideo.requestFocus()
                        return true
                    }
                    if (oldFullFocus?.id == R.id.episode_tab_trailer) {
                        mViewBinding.detailShVideo.requestFocus()
                        return true
                    }

                }

                KeyEvent.KEYCODE_DPAD_DOWN -> {
                    if (oldFullFocus?.id == R.id.layout_header_home) {
                        mViewBinding.detailShVideo.requestFocus()
                        return true
                    }
                    if (oldFullFocus?.id == R.id.layout_header_vip) {
                        mViewBinding.detailShVideo.requestFocus()
                        return true
                    }
                    if (oldFullFocus?.id == R.id.layout_header_mine) {
                        mViewBinding.detailShVideo.requestFocus()
                        return true
                    }
                    if (oldFullFocus?.id == R.id.layout_header_history) {
                        mViewBinding.detailShVideo.requestFocus()
                        return true
                    }
                    if (oldFullFocus?.id == R.id.layout_header_search) {
                        mViewBinding.detailShVideo.requestFocus()
                        return true
                    }

                }


                KeyEvent.KEYCODE_DPAD_RIGHT -> {

                    if (mViewBinding.detailRecommendList.hasFocus()) {
                        return mViewBinding.detailRecommendList.dispatchKeyEvent(event)
                    }
                }

                //向左 视图视图焦点处理
                KeyEvent.KEYCODE_DPAD_LEFT -> {
                    if (oldFullFocus == mViewBinding.btnVideoDetailHeaderFull || oldFullFocus == mViewBinding.clScaleBriefIntroduction) {
                        mViewBinding.detailShVideo.requestFocus()
                        return true
                    }

                    if (mViewBinding.detailRecommendList.hasFocus()) {
                        return mViewBinding.detailRecommendList.dispatchKeyEvent(event)
                    }

                }
            }
        }



        return super.dispatchKeyEvent(event)
    }

    private fun enableTryView(): Boolean {
        return isTryVideo
    }

    /**
     * 处理购买点击逻辑
     * 判断是否是教育
     */
    private fun onClickPayButton() {
        val detailDataId = if (dataType == Constant.DATA_TYPE_VRS) aid else vid
        //教育
        if (mVideoPayInfoResponse?.isEduCate == true) {
            if (mEduVip) return
            RequestManager.onEvent(
                "6_info",
                "6_info_btn_buy",
                detailDataId.toString(),
                null,
                null,
                null,
                null
            )
            setJumpToPayStatus()
            startPayActivity = false
            ActivityLauncher.startPayActivity(
                this,
                aid,
                mVideoPayInfoResponse?.feePayVid ?: 0,
                mVideoInfoResponse?.videoName ?: "",
                mVideoInfoResponse?.tvVerPic ?: "",
                1,
                PayActivity.PAY_SOURCE_DETAIL,
                21
            )

        } else {
            if (hasLogin) {
                if (hasVip) {
                    if (isTicket == true && isMember == false && isSingle == false) {
                        if ((ticketCount ?: 0) > 0) {
                            RequestManager.onEvent(
                                "6_info",
                                "6_info_btn_buy",
                                detailDataId.toString(),

                                "use_ticket",
                                null,
                                null,
                                null
                            )
                            setJumpToPayStatus()
                            ActivityLauncher.startTicketUseActivity(
                                this,
                                mVideoInfoResponse?.videoName ?: "",
                                mVideoInfoResponse?.tvVerPic ?: "",
                                aid!!.toLong(),
                                vid!!.toLong(),
                            )
                        } else {
                            //续费会员，立即赠券。本片因版权方要求，需要付费观看。
                            RequestManager.onEvent(
                                "6_info",
                                "6_info_btn_buy",
                                detailDataId.toString(),
                                null,
                                null,
                                null,
                                null
                            )
                            setJumpToPayStatus()

                            startPayActivity = false

                            ActivityLauncher.startPayActivityWithAidVid(
                                this,
                                aid,
                                vid,
                                ParamConstant.PAGE_SOURCE_PLAYER,
                                PayActivity.PAY_SOURCE_DETAIL,
                                false
                            )
                        }
                    } else if (isTicket == true && isSingle == true && isMember == false) {
                        if ((ticketCount ?: 0) > 0) {
                            RequestManager.onEvent(
                                "6_info",
                                "6_info_btn_buy",
                                detailDataId.toString(),

                                "use_ticket",
                                null,
                                null,
                                null
                            )
                            ActivityLauncher.startTicketUseActivity(
                                this,
                                mVideoInfoResponse?.videoName ?: "",
                                mVideoInfoResponse?.tvVerPic ?: "",
                                aid!!.toLong(),
                                vid!!.toLong()
                            )
                        } else {
                            //续费会员，立即赠券。本片因版权方要求，需要付费观看。
                            RequestManager.onEvent(
                                "6_info",
                                "6_info_btn_buy",
                                detailDataId.toString(),

                                null,
                                null,
                                null,
                                null
                            )
                            setJumpToPayStatus()

                            startPayActivity = false

                            ActivityLauncher.startPayActivityWithAidVid(
                                this,
                                aid,
                                vid,
                                ParamConstant.PAGE_SOURCE_PLAYER,
                                PayActivity.PAY_SOURCE_DETAIL,
                                false
                            )
                        }
                    } else if (isSingle == true && isTicket == false && isMember == false) {
                        RequestManager.onEvent(
                            "6_info",
                            "6_info_btn_buy",
                            detailDataId.toString(),

                            null,
                            null,
                            null,
                            null
                        )
                        startPayActivity = false
                        setJumpToPayStatus()

                        ActivityLauncher.startPayActivity(
                            this,
                            aid!!,
                            vid!!,
                            mVideoInfoResponse?.videoName ?: "",
                            mVideoInfoResponse?.tvVerPic ?: "",
                            1,
                            PayActivity.PAY_SOURCE_DETAIL,
                            0
                        )
                    } else {
                        RequestManager.onEvent(
                            "6_info",
                            "6_info_btn_buy",
                            aid.toString(),
                            null,
                            null,
                            null,
                            null
                        )
                        startPayActivity = false
                        setJumpToPayStatus()

                        ActivityLauncher.startPayActivityWithAidVid(
                            this,
                            aid,
                            vid,
                            ParamConstant.PAGE_SOURCE_PLAYER,
                            PayActivity.PAY_SOURCE_DETAIL,
                            false
                        )
                    }
                } else {
                    if (isTicket == true && isSingle == true && isMember == false) {
                        RequestManager.onEvent(
                            "6_info",
                            "6_info_btn_buy",
                            detailDataId.toString(),

                            null,
                            null,
                            null,
                            null
                        )
                        startPayActivity = false
                        setJumpToPayStatus()

                        ActivityLauncher.startPayActivity(
                            this,
                            aid!!,
                            vid!!,
                            mVideoInfoResponse?.videoName ?: "",
                            mVideoInfoResponse?.tvVerPic ?: "",
                            2,
                            PayActivity.PAY_SOURCE_DETAIL,
                            0
                        )
                    } else if (isSingle == true && isMember == false && isTicket == false) {
                        RequestManager.onEvent(
                            "6_info",
                            "6_info_btn_buy",
                            detailDataId.toString(),

                            null,
                            null,
                            null,
                            null
                        )
                        startPayActivity = false
                        setJumpToPayStatus()

                        ActivityLauncher.startPayActivity(
                            this,
                            aid!!,
                            vid!!,
                            mVideoInfoResponse?.videoName ?: "",
                            mVideoInfoResponse?.tvVerPic ?: "",
                            1,
                            PayActivity.PAY_SOURCE_DETAIL,
                            0
                        )
                    } else if (isMember == true && isSingle == true && isTicket == false) {
                        RequestManager.onEvent(
                            "6_info",
                            "6_info_btn_buy",
                            detailDataId.toString(),

                            null,
                            null,
                            null,
                            null
                        )
                        startPayActivity = false
                        setJumpToPayStatus()

                        ActivityLauncher.startPayActivity(
                            this,
                            aid!!,
                            vid!!,
                            mVideoInfoResponse?.videoName ?: "",
                            mVideoInfoResponse?.tvVerPic ?: "",
                            2,
                            PayActivity.PAY_SOURCE_DETAIL,
                            0
                        )
                    } else if (isMember == true && isSingle == false && isTicket == false) {
                        RequestManager.onEvent(
                            "6_info",
                            "6_info_btn_buy",
                            detailDataId.toString(),

                            null,
                            null,
                            null,
                            null
                        )
                        startPayActivity = false
                        setJumpToPayStatus()

                        ActivityLauncher.startPayActivityWithAidVid(
                            this,
                            aid,
                            vid,
                            ParamConstant.PAGE_SOURCE_PLAYER,
                            PayActivity.PAY_SOURCE_DETAIL,
                            false
                        )
                    } else {
                        RequestManager.onEvent(
                            "6_info",
                            "6_info_btn_buy",
                            detailDataId.toString(),
                            null,
                            null,
                            null,
                            null
                        )
                        setJumpToPayStatus()

                        startPayActivity = false
                        ActivityLauncher.startPayActivityWithAidVid(
                            this,
                            aid,
                            vid,
                            ParamConstant.PAGE_SOURCE_PLAYER,
                            PayActivity.PAY_SOURCE_DETAIL,
                            false
                        )
                    }
                }
            } else {
                if (isTicket == true && isSingle == false && isMember == false) {
                    //加入会员，立即赠券。本片因版权方要求，需要付费观看。
                    RequestManager.onEvent(
                        "6_info",
                        "6_info_btn_buy",
                        detailDataId.toString(),

                        null,
                        null,
                        null,
                        null
                    )
                    startPayActivity = false
                    setJumpToPayStatus()

                    ActivityLauncher.startPayActivityWithAidVid(
                        this,
                        aid,
                        vid,
                        ParamConstant.PAGE_SOURCE_PLAYER,
                        PayActivity.PAY_SOURCE_DETAIL,
                        false
                    )
                } else if (isSingle == true && isTicket == false && isMember == false) {
                    RequestManager.onEvent(
                        "6_info",
                        "6_info_btn_buy",
                        detailDataId.toString(),

                        null,
                        null,
                        null,
                        null
                    )
                    startPayActivity = false
                    setJumpToPayStatus()

                    ActivityLauncher.startPayActivity(
                        this,
                        aid!!,
                        vid!!,
                        mVideoInfoResponse?.videoName ?: "",
                        mVideoInfoResponse?.tvVerPic ?: "",
                        1,
                        PayActivity.PAY_SOURCE_DETAIL,
                        0
                    )
                } else if (isTicket == true && isSingle == true && isMember == false) {
                    //加入会员，立即赠券。本片因版权方要求，需要付费观看。
                    RequestManager.onEvent(
                        "6_info",
                        "6_info_btn_buy",
                        detailDataId.toString(),

                        null,
                        null,
                        null,
                        null
                    )
                    startPayActivity = false
                    setJumpToPayStatus()

                    ActivityLauncher.startPayActivity(
                        this,
                        aid!!,
                        vid!!,
                        mVideoInfoResponse?.videoName ?: "",
                        mVideoInfoResponse?.tvVerPic ?: "",
                        2,
                        PayActivity.PAY_SOURCE_DETAIL,
                        0
                    )
                } else if (isMember == true && isSingle == true && isTicket == false) {
                    //本片因版权方要求，需要付费观看。
                    RequestManager.onEvent(
                        "6_info",
                        "6_info_btn_buy",
                        detailDataId.toString(),

                        null,
                        null,
                        null,
                        null
                    )
                    startPayActivity = false
                    setJumpToPayStatus()

                    ActivityLauncher.startPayActivity(
                        this,
                        aid!!,
                        vid!!,
                        mVideoInfoResponse?.videoName ?: "",
                        mVideoInfoResponse?.tvVerPic ?: "",
                        2,
                        PayActivity.PAY_SOURCE_DETAIL,
                        0
                    )
                } else if (isMember == true && isSingle == false && isTicket == false) {
                    //本片因版权方要求，需要付费观看。
                    RequestManager.onEvent(
                        "6_info",
                        "6_info_btn_buy",
                        detailDataId.toString(),

                        null,
                        null,
                        null,
                        null
                    )
                    startPayActivity = false
                    setJumpToPayStatus()

                    ActivityLauncher.startPayActivityWithAidVid(
                        this,
                        aid,
                        vid,
                        ParamConstant.PAGE_SOURCE_PLAYER,
                        PayActivity.PAY_SOURCE_DETAIL,
                        false
                    )
                } else {
                    RequestManager.onEvent(
                        "6_info",
                        "6_info_btn_buy",
                        detailDataId.toString(),

                        null,
                        null,
                        null,
                        null
                    )
                    startPayActivity = false
                    setJumpToPayStatus()

                    ActivityLauncher.startPayActivityWithAidVid(
                        this,
                        aid,
                        vid,
                        ParamConstant.PAGE_SOURCE_PLAYER,
                        PayActivity.PAY_SOURCE_DETAIL,
                        false
                    )
                }
            }
        }

    }

    fun setResumeUserStatus(int: Int) {
        resumeUserStatus = int
    }


    fun setJumpToPayStatus() {
        if (isDts) {
            resumeUserStatus = 2
            return
        }
        if (feeType == FEE_VIDEO || feeType == FEE_ALBUM) {
            resumeUserStatus = 2
            return
        }
        resumeUserStatus = 1
        return
    }

    /**
     * 开启全屏
     */
    private fun startVideoWindowFull() {
        if (isFull) return
        mViewBinding.fragmentItemFocus.clearFocus()
        mViewBinding.detailShVideo.startLayoutParamsFull(
            mViewBinding.videoDetailDecorView,
            mViewBinding.detailShVideo
        )
        screenMode = PlayerConstants.ScreenMode.FULL
        mViewBinding.detailRecommendList.setVideoWindowFloating(false)
        mViewBinding.detailShVideo.requestFocus()
        showPlayControl()
        if (huaPingCheck()) {
            huaPingStartOrPause()
            return
        }
    }

    /**
     * 显示播放控制
     */
    fun showPlayControl() {
        if (!isFull) return
        if (filmPlayState == PlayerConstants.VideoState.PLAYING || filmPlayState == PlayerConstants.VideoState.PLAYING_BACK || filmPlayState == PlayerConstants.VideoState.BUFFERED || filmPlayState == PlayerConstants.VideoState.BUFFERING || filmPlayState == PlayerConstants.VideoState.PAUSED) {
            if (mVideoPlayMenuConfigComponent?.getIsShow() == true) return
            if (mVideoNextPlayPromptComponent?.getIsShow() == true) {
                mVideoNextPlayPromptComponent?.setIsCanShow(false)
            }
            mVideoPlayControlComponent?.show()
        }
    }

    /**
     * 恢复正常小窗
     */
    private fun startVideoWindowNormal() {
        if (isAllFull) return
        if (isNormal) return
        screenMode = PlayerConstants.ScreenMode.NORMAL
        val layoutParams = mViewBinding.detailShVideoStub.layoutParams
        layoutParams.width = resources.getDimension(R.dimen.x868).toInt()
        layoutParams.height = resources.getDimension(R.dimen.y488).toInt()
        mViewBinding.detailShVideoStub.layoutParams = layoutParams
        with(mViewBinding.detailShVideo) {
            this.setNormalScreenSize(
                resources.getDimension(R.dimen.x868).toInt(),
                resources.getDimension(R.dimen.y488).toInt()
            )
            mViewBinding.detailShVideo.startLayoutParamsNormal(this)
            mViewBinding.detailRecommendList.setVideoWindowFloating(false)
            mViewBinding.detailRecommendList.updateScrollViewMarginTopParams()
        }
        normalVideoFocus?.requestFocus()
        if (normalVideoFocus == mViewBinding.detailShVideo) {
            mViewBinding.fragmentItemFocus.visible()
            mViewBinding.fragmentItemFocus.setFocusView(mViewBinding.detailShVideo)
        } else {
            mViewBinding.fragmentItemFocus.gone()
            mViewBinding.fragmentItemFocus.clearFocus()
        }
        mVideoPlayControlComponent?.hide()
        if (huaPingCheck()) {
            huaPingStartOrPause()
            return
        }
    }

    /**
     * 悬浮窗
     */
    private fun startVideoWindowFloat() {
        if (isAllFull) return
        AppLogger.d(
            TAG,
            "startVideoWindowFloat mViewBinding.detailVideoInfo.hasFocus() ${mViewBinding.detailVideoInfo.hasFocus()} mViewBinding.headerViewVideoDetailUser.hasFocus() ${mViewBinding.headerViewVideoDetailUser.hasFocus()}"
        )
        if (isFull || mViewBinding.detailVideoInfo.hasFocus() ||
            mViewBinding.headerViewVideoDetailUser.hasFocus()
        ) {
            mViewBinding.detailRecommendList.setVideoWindowFloating(false)
            return
        }
        mViewBinding.fragmentItemFocus.gone()
        mViewBinding.fragmentItemFocus.clearFocus()
        val layoutParams = mViewBinding.detailShVideoStub.layoutParams
        layoutParams.width = resources.getDimension(R.dimen.x420).toInt()
        layoutParams.height = resources.getDimension(R.dimen.y236).toInt()
        mViewBinding.detailShVideoStub.layoutParams = layoutParams
        with(mViewBinding.detailShVideo) {
            this.setTinyScreenSize(
                resources.getDimension(R.dimen.x420).toInt(),
                resources.getDimension(R.dimen.y236).toInt()
            )
            mViewBinding.detailShVideo.startLayoutParamsTiny(this)
            screenMode = PlayerConstants.ScreenMode.TINY
            mViewBinding.detailRecommendList.setVideoWindowFloating(true)
            mViewBinding.detailRecommendList.updateScrollViewMarginTopParams()

        }
        mVideoPlayControlComponent?.hide()
        if (huaPingCheck()) {
            huaPingStartOrPause()
            return
        }
    }

    private fun showPlayConfigMenu(): Boolean {
        if (mVideoPlayMenuConfigComponent?.visibility != View.VISIBLE) {
            mVideoPlayMenuConfigComponent?.show()
            return true
        }
        return false
    }


    /**
     * 是否可以快进
     */
    private fun isFastForward(): Boolean {
        return (mVideoPlayMenuConfigComponent?.visibility != View.VISIBLE
                && isFull && !filmPlayState.playerIsNotStart())
    }


    override fun invalidate() {

    }


    /**
     * 切换清晰度
     */

    private fun changeResolutionAction(resolutionApp: ResolutionInfo, url: String?) {
        mVideoStartPreparingComponent?.isChangeResolutionApp = true
//        mVideoBufferLoadComponent?.isChangeResolutionApp = true
        mBaseOptionModel.startPosition = mPosition * 1000
        setVideoOption()
        mViewBinding.detailShVideo.changeResolution(
            resolutionApp.findPlayerResolution(),
            url ?: ""
        )
        ToastUtils.showToast(this, "已经为您切换为${resolutionApp.name}")
    }

    override fun onClickResolution(resolutionApp: ResolutionInfo) {
        currentResolutionInfo = resolutionApp
        onVvClose()
        reInitView = false

        AdTsManger.getInstants().setVideoClarity(
            resolutionApp.findPlayerResolution()
        )
        mCurrentUrl = resolutionApp.videoPlayInfo?.url ?: null
        if (hasPermissionCheckSuccess) {
            mCurrentUrl = mCurrentUrl?.plus("&mkey=$mVideoPermissionKey")
        }
        sendPlayDefinition(resolutionApp)

        if (Constant.DATA_TYPE_VRS == dataType) {
            resolutionApp.videoPlayInfo?.hasLogo?.let {
                mVideoLogoComponent?.setChangeResolutionEnableLogo(
                    it
                )
            }
        }
        mVideoPlayMenuConfigComponent?.setCurrentClarity(resolutionApp)
        PlaySettingHelper.setPlayClarity(resolutionApp.id ?: 1)
        if (tvPlayType == 3) {
            isChangeResolution = true
            requestDrmPlayInfo()
        } else {
            if (tvPlayType == 0) {
                appendAdTsUrl()
            }
            changeResolutionAction(resolutionApp, mCurrentUrl)
        }
    }

    /**
     * 外采drm请求播放链接信息等
     */
    private fun requestDrmPlayInfo() {
        val type = if (mBaseOptionModel.drmType == SofaPlayerConfig.SOFA_DRM_WIDEVINE) {
            1
        } else {
            3
        }
        mVideoDetailViewModel.requestDrmPlayInfo(
            currentResolutionInfo?.videoPlayInfo?.tvVerId,
            type, mVideoPermissionKey
        )
    }

    override fun onClickResolutionStartLogin(startResolutionApp: ResolutionInfo) {
        isStartLoginResolutionApp = !isDlna
        startLoginResolution = startResolutionApp
    }

    /**
     * 是否是切换需要登录/会员的清晰度
     */
    private var isStartLoginResolutionApp = false

    private var startLoginResolution: ResolutionInfo? = null


    /**
     * 请求推荐
     */
    private fun requestDetailRecommendAll() {
        if (reInitView || dataType != Constant.DATA_TYPE_VRS) {
            mVideoDetailViewModel.requestDetailRecommendAll(
                dataType,
                aid,
                vid
            )
        }
    }

    /**
     * 获取下一集信息
     */
    private fun getNextEpisodeInfo() {

        var videoOrder = mVideoPlayInfoResponse?.videoOrder ?: 0
        if (videoOrder == 0) {
            videoOrder = mVideoInfoResponse?.videoOrder ?: 0
        }
        mVideoDetailViewModel.getNextEpisodeInfo(
            aid = if (dataType == Constant.DATA_TYPE_VRS) getCurrentAid() else mVideoInfoResponse?.id,
            vid = vid,
            videoType = dataType,
            videoOrder = if (videoOrder == 0) mVideoInfoResponse?.videoOrder
                ?: 0 else videoOrder,
            isTrailer = isTrailerTab && mVideoPlayInfoResponse?.isisTrailer == true,
            tvCateCode,
            mViewBinding.detailRecommendList.getEpisodeGroupLayout()?.isLastEpisode(videoOrder)
                ?: -1,
            mViewBinding.detailRecommendList.getEpisodeGroupLayout()?.getEpisodeSortOrder()
                ?: 1,
            mViewBinding.detailRecommendList.getEpisodeGroupLayout()?.getPageSize()
                ?: 3,
            mViewBinding.detailRecommendList.getEpisodeGroupLayout()?.getEpisodeTotalCount()
                ?: 0
        )
    }

    override fun onClickHdrTips() {
        pauseVideo()
        mVideoHdrDetailComponent?.show(aid, vid)
    }

    fun pauseVideo() {
        mViewBinding?.detailShVideo?.pause()
    }

    /**
     * 只可在暂停时使用  内部判断了 播放状态
     */
    fun startVideo() {
        mViewBinding.detailShVideo.start()
    }

    /**
     * 切换选集
     */
    override fun onEpisodeClickVideo(
        aid: Int,
        historyId: Int,
        albumVid: Int,
        videoType: Int,
        isTrailerTab: Boolean
    ) {
        isChangeResolution = false
        onVvClose()
        onlySeeIndex = 0
        setStartPreparingComponentText(null)
        setPayHintText(HINT_TEXT_DEFAULT)
        mViewBinding.detailShVideo.release(clearState = true, clearRender = true)
        mViewBinding.fragmentItemFocus.gone()
        mViewBinding.fragmentItemFocus.clearFocus()
        if (normalVideoFocus != mViewBinding.detailShVideo) {
            normalVideoFocus = mViewBinding.btnVideoDetailHeaderFull
        }
        mVideoPlayMenuConfigComponent?.hide()
        dataType = videoType
        reInitView = false
        AppLogger.d(TAG, "onEpisodeClick  aid:$aid")
        this.isTrailerTab = isTrailerTab
        startVideoWindowFull()
        vid = albumVid
        mVideoDetailViewModel.requestVideo(
            videoType, aid = if (videoType == Constant.DATA_TYPE_VRS) aid else historyId,
            vid = albumVid, false, isChangeVideo = true
        )
    }

    override fun onEpisodeClickRelease() {
        isReplay = false
        mViewBinding.detailShVideo.release(false, false)
    }

    /**
     * 重新请求加载视频
     */
    private fun replayPayAfterVideo() {
        AppLogger.d(TAG, "replayPayAfterVideo")
        reInitView = false
        mVideoDetailViewModel.requestVideo(
            dataType,
            aid = aid,
            vid = if (dataType == Constant.DATA_TYPE_VRS) vid else mVideoInfoResponse?.serviceVid,
            false,
            isChangeVideo = true
        )
    }

    /**
     * 滑动视频变化更改
     */
    @Synchronized
    override fun onScrollWindowChange(headerViewIsFloating: Boolean) {
        AppLogger.d(
            TAG,
            "onScrollWindowChange isFull:$isFull  windowFloat:${windowFloat} headerViewIsFloating:$headerViewIsFloating"
        )
        if (isFull) return
        if (windowFloat != headerViewIsFloating) {
            mViewBinding.detailRecommendList.setVideoWindowFloating(headerViewIsFloating)
            if (headerViewIsFloating) {
                addPushEvent(10135, IMP, pathInfo = getInfoEvent {
                    it["pageId"] = "1042"
                }, null, null)
            }
            if (headerViewIsFloating) {
                startVideoWindowFloat()
            } else {
                startVideoWindowNormal()
            }
            updateHeaderWindow()
        }
    }

    //收藏
    fun postVideoDetailChase() {
        val collection = Collection()
        collection.albumId = aid
        collection.cateCode = tvCateCode
        collection.latestVideoCount = mVideoInfoResponse?.latestVideoCount
        collection.tvName = mVideoInfoResponse?.videoName
        collection.tvSets = mVideoInfoResponse?.maxVideoOrder
        collection.tvVerPic = mVideoInfoResponse?.tvVerPic
        collection.source = CollectionRecordHelper.COLLECTION_SOURCE_VOD
        collection.albumExtendsPic_640_360 = mVideoInfoResponse?.albumExtendsPic_640_360
        collection.ottFee = mVideoInfoResponse?.ottFee
        collection.tvIsFee = mVideoInfoResponse?.tvIsFee
        collection.cornerType = mVideoInfoResponse?.cornerType
        collection.tvDesc = mVideoInfoResponse?.dec
        collection.collectionTime = System.currentTimeMillis()
        collection.tvIsEarly = mVideoInfoResponse?.tvIsEarly
        collection.useTicket = mVideoInfoResponse?.useTicket
        collection.paySeparate = mVideoInfoResponse?.paySeparate
        mCollectionHelper.addRecord(collection)
    }

    //取消收藏
    fun postVideoDetailCancleChase() {
        mCollectionHelper.deleteRecordById(
            aid,
            mVideoInfoResponse?.latestVideoCount === mVideoInfoResponse?.maxVideoOrder,
            tvCateCode == Constant.EDU_CATE_CODE
        )
    }

    //当前播放专辑有可能是正片有可能是片花专辑，用当前播放videoinfo来判断，以展示选集
    //电影没有花絮，返回正片aid
    fun getCurrentAid() =
        if (mVideoPlayInfoResponse?.cateCode == Constant.CATECODE_MOVIE || mVideoPlayInfoResponse?.tvStype == 38) aid else (if (aid != mVideoPlayInfoResponse?.playlistId) mVideoPlayInfoResponse?.playlistId else aid)

    override fun onAdd(aid: Int, isSuccess: Boolean) {
        val toastText =
            if (hasLogin) "收藏成功，可在我的页面查看" else "收藏成功，登录后可同步账号"
        enableCollect = isSuccess
        if (isSuccess) {
            ToastUtils.showToast2(this.applicationContext, toastText)
        } else {
            ToastUtils.showToast2(this.applicationContext, "收藏失败")
        }
        setCollectionStateImg()


    }

    override fun onDelete(aid: Int, isSuccess: Boolean) {
        enableCollect = !isSuccess
        if (isSuccess) {
            ToastUtils.showToast2(this.applicationContext, "取消收藏")
        } else {
            ToastUtils.showToast2(this.applicationContext, "取消收藏失败！")
        }
        setCollectionStateImg()
    }

    override fun onRequestData(dataList: MutableList<*>?) {

    }

    override fun onRequestMoreData(dataList: MutableList<*>?) {
    }

    override fun onSelectRecord(isCollectRecordInChildVersion: Boolean) {
    }

    override fun onStartActivityPlay(isRePlay: Boolean) {
        if (!mInitPassport.equals(passport)) {
            this.isReplay = true
        } else {
            this.isReplay = isRePlay
        }
    }

    /**
     * 进度条菜单、清晰度菜单 展示隐藏回调
     */
    override fun onMenuControlIsVisible(isVisible: Boolean) {
        //试看
        if (enableTryView()) {
            //即将播放提示
            mVideoNextPlayPromptComponent?.setIsCanShow(false)
            //全屏
            if (isFull) {
                //菜单展示时
                if (isVisible) {
                    //设置不可展示试看view
                    mVideoTrySeeComponent?.canFullShow(false)
                } else {
                    //隐藏时判断是都其中一个 是否还在展示
                    if (mVideoPlayControlComponent?.getIsShow() == true) {
                        mVideoPlayMenuConfigComponent?.hide()
                        return
                    }
                    if (mVideoPlayMenuConfigComponent?.getIsShow() == true) {
                        return
                    }
                    mVideoTrySeeComponent?.canFullShow(true)
                }
            } else {
                mVideoTrySeeComponent?.canFullShow(true)
            }
        } else {
            //隐藏时判断是都其中一个 是否还在展示
            if (mVideoPlayControlComponent?.getIsShow() == true || mVideoPlayMenuConfigComponent?.getIsShow() == true) {
                //即将播放提示
                mVideoNextPlayPromptComponent?.setIsCanShow(false)
                return
            }
            //即将播放提示
            mVideoNextPlayPromptComponent?.setIsCanShow(true)
        }
    }

    override fun visibilityChange(controlComponent: ShControlComponent) {
        when (controlComponent) {
            mVideoStartPreparingComponent -> {
                if (mVideoStartPreparingComponent?.componentVisibility() == true) {
                    mVideoAdHeaderComponent?.hide()
                    mVideoPromptCopyComponent?.hide()
                }
            }
        }
    }

    private inner class AllOnStateChangeListener : OnStateChangeListener {
        override fun onScreenModeChanged(screenMode: Int) {
            <EMAIL> = screenMode
            //设置全屏是列表不可滑动
            mViewBinding.detailRecommendList.isNestedScrollingEnabled = !isFull
            if (!isNormal) {
                mViewBinding.fragmentItemFocus.gone()
                mViewBinding.fragmentItemFocus.clearFocus()
            }
        }

        override fun onPlayerStateChanged(playState: Int, extras: HashMap<String, Any>) {
            videoPlayState = playState
            if ((playState.playerIsPlaying()) && !isResume) {
                mViewBinding.detailShVideo.pause()
            }
            if (playState.playerIsPlaying() && !southMediaSuccess) {
                setPayHintText(HINT_TEXT_NAN_CHUAN)
                mViewBinding.detailShVideo.release(false, false)
            }
        }
    }

    private inner class AdOnStateChangeListener : OnStateChangeListener {
        override fun onPlayerStateChanged(playState: Int, extras: HashMap<String, Any>) {
            adPlayState = playState
        }
    }


    private inner class FilmOnStateChangeListener : OnStateChangeListener {

        override fun onPlayerStateChanged(playState: Int, extras: HashMap<String, Any>) {
            filmPlayState = playState
            SohuDlnaManger.getInstance().isPlaying = !playState.playerIsNotStart()
            //buffer 控件添加和移除
            if (playState == PlayerConstants.VideoState.PLAYING || playState == PlayerConstants.VideoState.PLAYING_BACK) {
                if (!mViewBinding.detailShVideo.filmContainsControlComponents(
                        mVideoBufferLoadComponent!!
                    )
                ) {
                    mViewBinding.detailShVideo.addFilmVideoControlComponent(
                        mVideoBufferLoadComponent!!
                    )
                    PlayerLogger.w("addFilmVideoControlComponent mVideoBufferLoadComponent")
                }

            } else {
                if (mViewBinding.detailShVideo.filmContainsControlComponents(
                        mVideoBufferLoadComponent!!
                    ) && playState != PlayerConstants.VideoState.BUFFERING && playState != PlayerConstants.VideoState.BUFFERED
                ) {
                    PlayerLogger.w("removeFilmVideoControlComponent mVideoBufferLoadComponent")
                    mViewBinding.detailShVideo.removeFilmVideoControlComponent(
                        mVideoBufferLoadComponent!!
                    )
                }
            }
            //播放状态逻辑细处理
            when (playState) {
                PlayerConstants.VideoState.PREPARED -> {
                    onVvStart()
                    startUpdateProgress()
                    val speed = if (reInitView) {
                        val playSpeed = UserConstants.PE_PLAY_RATE_NORMAL
                        PlaySettingHelper.setPlaySpeed(playSpeed)
                        playSpeed
                    } else {
                        PlaySettingHelper.getPlaySpeed()
                    }
                    mViewBinding.detailShVideo.setSpeed(speed)
                }

                PlayerConstants.VideoState.BUFFERING -> {
                    stopUpdateProgress()
                }

                PlayerConstants.VideoState.PAUSED -> {
                    stopUpdateProgress()
                    saveHistory()
                    SohuDlnaManger.getInstance().sendPauseEvent()
                }

                PlayerConstants.VideoState.PLAYING_BACK,
                PlayerConstants.VideoState.PLAYING,
                PlayerConstants.VideoState.BUFFERED -> {
                    startUpdateProgress()
                    if (isFull) {
                        showPlayControl()
                    }
                    setPayHintText(HINT_TEXT_DEFAULT)
                    SohuDlnaManger.getInstance().sendPlayEvent()
                }

                PlayerConstants.VideoState.IDLE -> {
                    stopUpdateProgress()
                    saveHistory()
                }

                PlayerConstants.VideoState.ERROR -> {
                    val errorCode = extras[PlayerConstants.MapExtKey.ERROR_CODE] ?: 0
                    val errorInfo = extras[PlayerConstants.MapExtKey.ERROR_INFO] as Throwable?
                    stopUpdateProgress()
                    AppLogger.e(
                        TAG,
                        "error code is :$errorCode  error message:${errorInfo?.message ?: "其他未知错误"}"
                    )
                    mViewBinding.detailShVideo.release(clearState = false, clearRender = false)
                    if (errorInfo is PlayerException) {
                        if (tvPlayType == 3 && mBaseOptionModel.drmType == SofaPlayerConfig.SOFA_DRM_WIDEVINE) {
                            tvPlayType = 3
                            mBaseOptionModel.drmType = SofaPlayerConfig.SOFA_DRM_MARLIN
                            requestDrmPlayInfo()
                            //异步起播
                            AppLogger.d(
                                TAG,
                                "设备使用drmType播放器硬解失败切换sohu drm: ${mBaseOptionModel.drmType}"
                            )
                            return
                        }
                    }
                    if (isFull) {
                        startVideoWindowNormal()
                    }
                    AdTsManger.getInstants().reportAdTsError(-1, extras.toString())
                    //当drmLicense请求失败或者过期播放器报错并返回错误码  其他错误码对照 代码或者sofa 错误码文档
                    if (errorCode == 40701 || errorCode == 40702) {
                        setPayHintText(HINT_TEXT_DRM_ERROR)
                    } else {
                        setPayHintText(HINT_TEXT_ERROR)
                    }
                    SohuDlnaManger.getInstance().sendStopEvent()
                }

                PlayerConstants.VideoState.PLAYBACK_COMPLETED -> {
                    AppLogger.d(TAG, "播放完成下一集逻辑")
                    onlySeeIndex = 0
                    stopUpdateProgress()
                    mVideoPlayMenuConfigComponent?.removeCallBacks()
                    mViewBinding.detailShVideo.release(clearState = false, true)
                    onVvComplete()
                    startPayActivity = false
                    if (feeType == FEE_FREE || mVideoPermissionKey?.isNotEmpty() == true) {
                        if (nextEpisodeInfo.size == 5) {
                            val videoType = nextEpisodeInfo[2] as Int
                            val videoVid = nextEpisodeInfo[1] as Int
                            mVideoDetailViewModel.requestVideo(
                                videoType,
                                aid = if (Constant.DATA_TYPE_VRS == videoType) getCurrentAid() else videoVid,
                                vid = videoVid,
                                false,
                                isChangeVideo = true
                            )
                        } else if (nextEpisodeInfo.size == 1) {
                            if (nextEpisodeInfo[0] as Int == -1) {
                                if (isFull) {
                                    startVideoWindowNormal()
                                }
                                setPayHintText(
                                    HINT_TEXT_ERROR_OFFLINE
                                )

                            } else {
                                if (isDlna) {
                                    //试看结束和付费视频鉴权失败 提示付费
                                    SohuDlnaManger.finishPlayingVideoView(true)
                                    return
                                }
                                if (isFull) {
                                    startVideoWindowNormal()
                                }
                                setPayHintText(HINT_TEXT_FINISH)
                            }

                            if (nextEpisodeInfo[0] != -1) {
                                saveHistory()
                                onPlayCompleted(nextEpisodeInfo[0] as Int)
                            }
                        } else {
                            if (isDlna) {
                                SohuDlnaManger.finishPlayingVideoView(true)
                                return
                            }
                            if (isFull) {
                                startVideoWindowNormal()
                            }
                            setPayHintText(HINT_TEXT_FINISH)
                            saveHistory()
                            onPlayCompleted(0)
                        }

                    } else if (isTryFinish()) {
                        if (!windowFloat) {
                            showPayOrTicketDialog()
                        }

                    } else {
                        //搜狐视频的来源投屏
                        if (isShDlnaVideo) {
                            //试看结束和付费视频鉴权失败 提示付费
                            if (mDlnaMode?.isTrySee == true || TextUtils.isEmpty(
                                    mVideoPermissionKey
                                )
                            ) {
                                AppLogger.e(
                                    TAG,
                                    "鉴权失败 -》isTrySee: " + mDlnaMode?.isTrySee + " mKey is : $mVideoPermissionKey"
                                )
                                setPayHintText(HINT_TEXT_DLNA_PERMISSION_KEY_FAIL)

                            }
                            return
                        }
                        showPayOrTicketDialog()
                    }
                    if (!isShDlnaVideo) {
                        SohuDlnaManger.finishPlayingVideoView(true)
                    }

                }
            }
        }
    }

    private var isScreenOFF = false

    inner class ScreenStateReceiver() : BroadcastReceiver() {

        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            if (Intent.ACTION_SCREEN_ON == action) {
                // 屏幕开启
                AppLogger.d(TAG, "Screen is ON")
            } else if (Intent.ACTION_SCREEN_OFF == action) {
                isScreenOFF = true
                try {
                    mViewBinding.detailShVideo.release()
                } catch (e: Throwable) {
                    AppLogger.d(TAG, "Screen is OFF error:${e.localizedMessage}")
                }
                // 屏幕关闭
                AppLogger.d(TAG, "Screen is OFF")
            }

        }
    }
}