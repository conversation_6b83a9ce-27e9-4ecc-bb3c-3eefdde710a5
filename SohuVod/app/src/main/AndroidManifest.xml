<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <!-- 允许程序访问有关GSM网络信息 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 允许程序访问Wi-Fi网络状态信息 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 允许程序改变网络连接状态 -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" /> <!-- 允许程序改变Wi-Fi连接状态 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- 允许程序打开网络套接字 -->
    <uses-permission android:name="android.permission.INTERNET" /> <!-- &lt;!&ndash; 允许装载和卸载文件系统权限 &ndash;&gt; -->
    <!-- <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" /> -->
    <!-- 读外部存储的权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- 允许程序读取底层系统日志文件 -->
    <!-- <uses-permission android:name="android.permission.READ_LOGS" /> -->
    <!-- 电话状态 -->
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" /> <!-- 允许程序在手机屏幕关闭后后台进程仍然运行写入GPRS接入点设置 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- 允许程序写入外部存储，如SD卡上写文件 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- 未知来源安装权限 -->
    <!-- <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> -->
    <uses-permission android:name="${JIMI_BROADCAST_PERMISSION}" /> <!-- 分渠道添加开机权限 -->
    <permission android:name="${SOHU_PERMISSION_BOOT}" />

    <uses-permission android:name="${SOHU_PERMISSION_BOOT}" />

    <uses-feature
        android:name="android.hardware.type.television"
        android:required="true" />

    <application
        android:name=".app.SohuAppNew"
        android:allowBackup="false"
        android:icon="@mipmap/app_icon"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:theme="@style/Theme.AppCompat.Light.NoActionBar"
        android:usesCleartextTraffic="true"
        android:hardwareAccelerated="true"
        tools:replace="android:allowBackup">
        <activity
            android:name=".activity.IssuesFeedbackActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustUnspecified|stateAlwaysHidden" />
        <activity
            android:name=".videodetail.activity.VideoActivity"
            android:exported="false" /> <!-- <activity -->
        <!-- android:name=".videodetail.activity.VideoDetailActivityNew" -->
        <!-- android:exported="false" -->
        <!-- android:launchMode="singleTask" /> -->
        <activity
            android:name=".activity.launcher.MainActivity"
            android:exported="false" />
        <activity
            android:name=".activity.permission.StoragePermissionsActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="landscape"
            android:theme="@style/ActivityDialogStyle"
            android:windowSoftInputMode="adjustUnspecified|stateAlwaysHidden" />
        <activity
            android:name=".activity.configuration.ConfigurationListActivity"
            android:exported="false"
            android:launchMode="singleTop">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name=".activity.setting.DownloadUserInfoActivity"
            android:exported="false" />
        <activity
            android:name=".activity.PayInfoActivity"
            android:exported="false" />
        <activity
            android:name=".search.SearchActivity"
            android:exported="false" />
        <activity
            android:name=".activity.setting.SettingActivity"
            android:exported="false" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.sohuott.tv.vod.FileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider> <!-- <activity -->
        <!-- android:name=".activity.BootActivity" -->
        <!-- android:launchMode="singleTask" -->
        <!-- android:theme="@style/StartStyle"> -->
        <!-- <intent-filter> -->
        <!-- <action android:name="android.intent.action.MAIN" /> -->
        <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
        <!-- </intent-filter> -->
        <!-- <intent-filter> -->
        <!-- <action android:name="${BOOT_ACTION}" /> -->
        <!-- <category android:name="android.intent.category.DEFAULT" /> -->
        <!-- </intent-filter> -->
        <!-- </activity> -->
        <activity
            android:name=".activity.launcher.LauncherActivity"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"
            android:theme="@style/StartStyle">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="${BOOT_ACTION}" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activity.ListVideoActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen">

            <!-- <intent-filter> -->
            <!-- <data -->
            <!-- android:host="sohu.tv" -->
            <!-- android:path="/listvideo" -->
            <!-- android:scheme="yt" /> -->
            <!-- <action android:name="android.intent.action.VIEW" /> -->


            <!-- <category android:name="android.intent.category.DEFAULT" /> -->
            <!-- <category android:name="android.intent.category.BROWSABLE" /> -->
            <!-- </intent-filter> -->
            <intent-filter>
                <data
                    android:host="sohu.tv"
                    android:path="/listvideo.internal"
                    android:scheme="yt" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <!-- <intent-filter> -->
            <!-- <action android:name="${LISTVIDEO_ACTION}" /> -->
            <!-- <action android:name="android.intent.action.VIEW" /> -->


            <!-- <category android:name="android.intent.category.DEFAULT" /> -->
            <!-- </intent-filter> -->
        </activity>
        <activity
            android:name=".activity.GridListActivityNew"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen">

            <!-- <intent-filter> -->
            <!-- <data -->
            <!-- android:host="sohu.tv" -->
            <!-- android:path="/gridlist" -->
            <!-- android:scheme="yt" /> -->
            <!-- <action android:name="android.intent.action.VIEW" /> -->


            <!-- <category android:name="android.intent.category.DEFAULT" /> -->
            <!-- <category android:name="android.intent.category.BROWSABLE" /> -->
            <!-- </intent-filter> -->
            <intent-filter>
                <data
                    android:host="sohu.tv"
                    android:path="/gridlist.internal"
                    android:scheme="yt" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <!-- <intent-filter> -->
            <!-- <action android:name="${GRIDLIST_ACTION}" /> -->
            <!-- <action android:name="android.intent.action.VIEW" /> -->


            <!-- <category android:name="android.intent.category.DEFAULT" /> -->
            <!-- </intent-filter> -->
        </activity>
        <activity
            android:name=".activity.ActorListActivity"
            android:launchMode="singleTask"
            android:theme="@style/ActivityDialogStyle" />
        <activity
            android:name=".activity.VideoDetailMoreActivity"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"
            android:theme="@style/ActivityDialogStyle"
            android:windowSoftInputMode="adjustUnspecified|stateAlwaysHidden" />
        <activity
            android:name=".activity.GridListTagActivityNew"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name=".activity.ListUserRelatedActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen">

            <!-- <intent-filter> -->
            <!-- <data -->
            <!-- android:host="sohu.tv" -->
            <!-- android:path="/listuserrelated" -->
            <!-- android:scheme="yt" /> -->
            <!-- <action android:name="android.intent.action.VIEW" /> -->


            <!-- <category android:name="android.intent.category.DEFAULT" /> -->
            <!-- <category android:name="android.intent.category.BROWSABLE" /> -->
            <!-- </intent-filter> -->
            <intent-filter>
                <data
                    android:host="sohu.tv"
                    android:path="/listuserrelated.internal"
                    android:scheme="yt" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <!-- <intent-filter> -->
            <!-- <action android:name="${LISTUSER_ACTION}" /> -->
            <!-- <action android:name="android.intent.action.VIEW" /> -->


            <!-- <category android:name="android.intent.category.DEFAULT" /> -->
            <!-- </intent-filter> -->
        </activity>
        <activity
            android:name=".activity.ListEduUserRelatedActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen">
            <intent-filter>
                <data
                    android:host="sohu.tv"
                    android:path="/eduuserrelated"
                    android:scheme="yt" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <intent-filter>
                <data
                    android:host="sohu.tv"
                    android:path="/eduuserrelated.internal"
                    android:scheme="yt" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <intent-filter>
                <action android:name="${EDUUSER_ACTION}" />
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activity.ProducerActivity"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name=".activity.WelfareActivity"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name=".activity.RenewActivity"
            android:launchMode="singleTask"
            android:theme="@style/ActivityDialogStyle" />
        <activity
            android:name=".activity.TeenModeDescActivity"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name=".activity.LabelGridListActivity"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name=".activity.dlna.DlnaPlayerActivity"
            android:exported="true"
            android:theme="@style/Theme.AppCompat.NoActionBar" />

        <!-- android:name=".activity.SearchActivity" -->
        <!-- android:exported="true" -->
        <!-- android:launchMode="singleTask" -->
        <!-- android:screenOrientation="landscape" -->
        <!-- android:theme="@android:style/Theme.NoTitleBar.Fullscreen" -->
        <!-- android:windowSoftInputMode="adjustUnspecified|stateAlwaysHidden"> -->
        <!-- <intent-filter> -->
        <!-- <action android:name="com.sohuott.tv.vod.action.SEARCH" /> -->
        <!-- <category android:name="android.intent.category.DEFAULT" /> -->
        <!-- </intent-filter> -->
        <!-- </activity> -->
        <activity
            android:name=".activity.SearchInputActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name=".activity.SearchResultActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen">

            <!-- <intent-filter> -->
            <!-- <data -->
            <!-- android:host="sohu.tv" -->
            <!-- android:path="/search" -->
            <!-- android:scheme="yt" /> -->
            <!-- <action android:name="android.intent.action.VIEW" /> -->


            <!-- <category android:name="android.intent.category.DEFAULT" /> -->
            <!-- <category android:name="android.intent.category.BROWSABLE" /> -->
            <!-- </intent-filter> -->
            <intent-filter>
                <data
                    android:host="sohu.tv"
                    android:path="/search.internal"
                    android:scheme="yt" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <!-- <intent-filter> -->
            <!-- <action android:name="com.sohuott.tv.vod.action.SEARCH" /> -->
            <!-- <category android:name="android.intent.category.DEFAULT" /> -->
            <!-- </intent-filter> -->
        </activity>
        <activity
            android:name=".activity.LoginActivity"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"
            android:theme="@style/ActivityDialogStyle"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize" />
        <activity
            android:name=".activity.PayActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"
            android:theme="@style/ActivityDialogStyle"
            android:windowSoftInputMode="adjustUnspecified|stateAlwaysHidden">

            <!-- <intent-filter> -->
            <!-- <data -->
            <!-- android:host="sohu.tv" -->
            <!-- android:path="/pay" -->
            <!-- android:scheme="yt" /> -->
            <!-- <action android:name="android.intent.action.VIEW" /> -->


            <!-- <category android:name="android.intent.category.DEFAULT" /> -->
            <!-- <category android:name="android.intent.category.BROWSABLE" /> -->
            <!-- </intent-filter> -->
            <intent-filter>
                <data
                    android:host="sohu.tv"
                    android:path="/pay.internal"
                    android:scheme="yt" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <!-- <intent-filter> -->
            <!-- <action android:name="${PAY_ACTION}" /> -->
            <!-- <category android:name="android.intent.category.DEFAULT" /> -->
            <!-- </intent-filter> -->
        </activity>
        <activity
            android:name=".activity.WechatPublicActivity"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen"
            android:windowSoftInputMode="adjustUnspecified|stateAlwaysHidden" />
        <activity
            android:name=".activity.MyMessageActivity"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name=".activity.NewNetworkDialogActivity"
            android:label="@string/network_dialog_title"
            android:launchMode="singleTask"
            android:theme="@style/NetworkDialog" />
        <activity
            android:name=".activity.TokenExpiredActivity"
            android:label="@string/token_expired_title"
            android:theme="@style/NetworkDialog" />
        <activity
            android:name=".activity.PGidExceptionActivity"
            android:label="@string/token_expired_title"
            android:theme="@style/NetworkDialog" />
        <activity
            android:name=".activity.AboutActivity"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />

        <activity
            android:name=".activity.TicketUseActivity"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name=".activity.TvHelperActivity"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name=".activity.TempletActivity"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen">
            <intent-filter>
                <data
                    android:host="sohu.tv"
                    android:path="/templet"
                    android:scheme="yt" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <intent-filter>
                <data
                    android:host="sohu.tv"
                    android:path="/templet.internal"
                    android:scheme="yt" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activity.PersonalCinemaActivity"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- <activity -->
        <!-- android:name=".activity.LivingPlayerActivity" -->
        <!-- android:launchMode="singleTask" -->
        <!-- android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> -->
        <activity
            android:name=".activity.SubjectActivity"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name=".activity.teenagers.TeenagersActivity"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name=".activity.setting.ShowPrivacyWebViewActivity"
            android:launchMode="singleTop"
            android:theme="@style/ActivityDialogStyle" />
        <activity
            android:name=".activity.detective.PlayDetectionActivity"
            android:exported="false" /> <!-- 设置singleTask 会调至setresult无效 -->
        <activity
            android:name=".activity.TeenagerLockActivity"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
        <activity
            android:name=".activity.AccountLogOffActivity"
            android:exported="false" />
        <activity
            android:name=".activity.ProcessActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen">
            <intent-filter>
                <data
                    android:host="sohu.tv"
                    android:scheme="yt" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <intent-filter>
                <action android:name="${DETAIL_ACTION}" />
                <action android:name="${LISTVIDEO_ACTION}" />
                <action android:name="${GRIDLIST_ACTION}" />
                <action android:name="${LISTUSER_ACTION}" />
                <action android:name="${SEARCH_ACTION}" />
                <action android:name="${PALY_ACTION}" />
                <action android:name="${PAY_ACTION}" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>

        <receiver
            android:name=".receiver.VodReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <action android:name="com.sohu.app.receiver.token_expired" />
                <action android:name="com.sohu.app.receiver.invalid_account" />
                <action android:name="com.sohu.app.receiver.freeze_account" />
                <action android:name="action_net_changed" />
            </intent-filter>
        </receiver>


        <service android:name=".receiver.CancelService" /> <!-- <service android:name="com.android.cast.dlna.dmr.DLNARendererService" /> -->
        <!--
        <service
            android:name=".service.SystemDialogService"
            android:enabled="true"
            android:exported="true">
        </service>
        -->
        <!--
 <receiver
                android:name=".receiver.VodBootReceiver"
                android:enabled="true"
                android:exported="true"
                android:permission="${SOHU_PERMISSION_BOOT}">
                <intent-filter android:priority="1000">
                    <action android:name="${SOHU_ACTION_BOOT}"/>
                    <category android:name="android.intent.category.DEFAULT"/>
                </intent-filter>
            </receiver>
        <receiver android:name=".receiver.WakeReceiver"
                android:enabled="true"
                android:exported="true">
                <intent-filter android:priority="1000">
                    <action android:name="action_wake_guard"/>
                </intent-filter>
            </receiver>
        -->
        <!-- <service android:name="com.sohuott.tv.vod.service.VoiceControlService"> -->
        <!-- <intent-filter> -->
        <!-- <action android:name="sohu.intent.action.REMOTE_VOICECONTROL_SERVICE" /> -->
        <!-- </intent-filter> -->
        <!-- </service> -->
        <service
            android:name=".partner.SohuAidlService"
            android:exported="false">
            <intent-filter>
                <action android:name="sohu.intent.action.REMOTE_SOHU_AIDL_SERVICE" />
            </intent-filter>
        </service>

        <meta-data
            android:name="SOHUPUSH_APPKEY"
            android:value="${SOHUPUSH_APPKEY}" />
        <meta-data
            android:name="SOHUPUSH_PID"
            android:value="${SOHUPUSH_PID}" />
        <meta-data
            android:name="SOHUPUSH_CHANNEL"
            android:value="test" />
        <meta-data
            android:name="API_KEY"
            android:value="4eda5b5735fb430b2e8dfba6c003941b" />
        <meta-data
            android:name="PARTNER_NO"
            android:value="\${PARTNER_NO}" />
        <meta-data
            android:name="PARTNER_NO2"
            android:value="\${PARTNER_NO2}" />
        <meta-data
            android:name="PLATFORM_ID"
            android:value="${PLATFORM_ID}" />
        <meta-data
            android:name="PLATFORM_CODE"
            android:value="${PLATFORM_CODE}" />
        <meta-data
            android:name="PALY_ACTION"
            android:value="${PALY_ACTION}" />
        <meta-data
            android:name="LIVETV_ACTION"
            android:value="${LIVETV_ACTION}" />
        <meta-data
            android:name="VRPALY_ACTION"
            android:value="${VRPALY_ACTION}" />
        <meta-data
            android:name="DETAIL_ACTION"
            android:value="${DETAIL_ACTION}" />
        <meta-data
            android:name="LISTVIDEO_ACTION"
            android:value="${LISTVIDEO_ACTION}" />
        <meta-data
            android:name="LISTUSER_ACTION"
            android:value="${LISTUSER_ACTION}" />
        <meta-data
            android:name="EDUUSER_ACTION"
            android:value="${EDUUSER_ACTION}" />
        <meta-data
            android:name="GRIDLIST_ACTION"
            android:value="${GRIDLIST_ACTION}" />
        <meta-data
            android:name="PRODUCT_ID"
            android:value="11" />
        <meta-data
            android:name="APP_ID"
            android:value="22" /> <!-- 配置APP ID -->
        <meta-data
            android:name="BUGLY_APPID"
            android:value="900029252" /> <!-- 配置APP版本号 -->
        <meta-data
            android:name="BUGLY_APP_VERSION"
            android:value="10.01" /> <!-- 配置APP渠道号 -->
        <meta-data
            android:name="BUGLY_APP_CHANNEL"
            android:value="${PARTNER_NO}" /> <!-- 配置Bugly调试模式(true或者false) -->
        <meta-data
            android:name="BUGLY_ENABLE_DEBUG"
            android:value="true" /> <!-- 按渠道配置是否监听开机广播 -->
        <meta-data
            android:name="SOHU_ACTION_BOOT"
            android:value="${SOHU_ACTION_BOOT}" />
        <meta-data
            android:name="SOHU_ACTION_NETWORK_CHANGE"
            android:value="${SOHU_ACTION_NETWORK_CHANGE}" /> <!--  -->
        <meta-data
            android:name="SOHU_PERMISSION_BOOT"
            android:value="${SOHU_PERMISSION_BOOT}" /> <!-- 极米渠道添加权限 -->
        <meta-data
            android:name="JIMI_BROADCAST_PERMISSION"
            android:value="${JIMI_BROADCAST_PERMISSION}" /> <!-- 按渠道添加是否展示全局dia -->
        <meta-data
            android:name="IS_GLOBAL_DIA_ACTIVE"
            android:value="${IS_GLOBAL_DIA_ACTIVE}" /> <!-- 按渠道添加定时发送历史记录的频率 -->
        <meta-data
            android:name="VIDEO_HISTORY_DURATION"
            android:value="${VIDEO_HISTORY_DURATION}" />
        <meta-data
            android:name="APK_NUMBER"
            android:value="${APK_NUMBER}" />
    </application>

</manifest>